import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from tqdm import tqdm
from typing import Dict, Tuple, Optional, List
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import json
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import torch.nn.functional as F
from skopt import gp_minimize
from skopt.space import Real, Integer, Categorical

# 导入项目模块
from data_preprocessing import DataPreprocessor
from model import create_model
from advanced_bayesian_optimizer import AdvancedBayesianOptimizer
from optimization_visualizer import create_optimization_report

# 导入统一的配置和日志系统
from config import get_config, ConfigManager
from logger import get_logger, ProgressLogger

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_output_dir():
    """创建输出目录"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join('outputs', timestamp)
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def save_plot(fig, name, output_dir):
    """保存图表"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{name}_{timestamp}.png"
    filepath = os.path.join(output_dir, filename)
    fig.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close(fig)
    return filepath

def save_table(df, name, output_dir):
    """保存表格"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{name}_{timestamp}.csv"
    filepath = os.path.join(output_dir, filename)
    df.to_csv(filepath, index=False, encoding='utf-8-sig')
    return filepath

def convert_params_types(params):
    """转换参数中的numpy类型为Python原生类型"""
    if params is None:
        return None

    converted_params = {}

    # 需要转换为int的参数
    integer_params = [
        'num_layers', 'hidden_size', 'kernel_size', 'gru_layers',
        'batch_size', 'sequence_length', 'k_best_features',
        'early_stopping_patience', 'lr_scheduler_patience', 'input_size', 'output_size'
    ]

    for key, value in params.items():
        if key in integer_params:
            # 确保整数参数是Python原生int类型
            if hasattr(value, 'item'):  # numpy scalar
                converted_params[key] = int(value.item())
            else:
                converted_params[key] = int(value)
        elif hasattr(value, 'item'):  # 其他numpy scalar
            converted_params[key] = value.item()
        elif isinstance(value, (list, tuple)):
            converted_params[key] = [v.item() if hasattr(v, 'item') else v for v in value]
        else:
            converted_params[key] = value

    return converted_params

def save_best_params(params, output_dir):
    """保存最优参数，处理numpy类型转换"""
    # 转换numpy类型为Python原生类型
    serializable_params = convert_params_types(params)

    params_path = os.path.join(output_dir, 'best_params.json')
    with open(params_path, 'w') as f:
        json.dump(serializable_params, f, indent=4)
    return params_path

def load_best_params(output_dir):
    """加载最优参数"""
    params_path = os.path.join(output_dir, 'best_params.json')
    if os.path.exists(params_path):
        with open(params_path, 'r') as f:
            return json.load(f)
    return None

class SamePadConv1d(nn.Module):
    """1D Convolution with 'same' padding."""
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, dilation=1, groups=1, bias=True):
        super().__init__()
        self.kernel_size = kernel_size
        self.stride = stride
        self.dilation = dilation
        self.conv = nn.Conv1d(
            in_channels, out_channels, kernel_size, 
            stride=stride, dilation=dilation, groups=groups, bias=bias, padding=0
        )

    def forward(self, x):
        # Asymmetric padding for stride=1
        padding_total = (self.kernel_size - 1) * self.dilation
        padding_left = padding_total // 2
        padding_right = padding_total - padding_left
        x = F.pad(x, (padding_left, padding_right))
        return self.conv(x)

class EVChargingPredictor:
    def __init__(
        self,
        sequence_length: int = 24,
        device: str = 'cuda'
    ):
        """初始化预测器

        Args:
            sequence_length (int, optional): 序列长度. Defaults to 24.
            device (str, optional): 设备, 默认使用 CUDA. 如果 CUDA 不可用会抛出错误，提示用户开启 GPU. Defaults to 'cuda'.
        """
        if device.startswith('cuda') and not torch.cuda.is_available():
            raise RuntimeError("检测到 device='cuda' 但当前环境无可用 GPU，请检查 CUDA 驱动或设置 device='cpu'。")

        self.sequence_length = sequence_length
        self.device = device
        self.preprocessor = DataPreprocessor()
        self.output_dir = create_output_dir()
        
    def prepare_data(self, file_path: str) -> Tuple[DataLoader, DataLoader, DataLoader, MinMaxScaler]:
        """准备训练、验证和测试数据，并返回目标值缩放器"""
        # 1. 加载和预处理数据
        df = self.preprocessor.load_and_clean_data(file_path)
        df = self.preprocessor.fill_missing_values(df)
        df = df.sort_values('Timestamp')

        # 2. 按比例切分 (Before any augmentation)
        total_samples = len(df)
        train_size = int(0.7 * total_samples)
        val_size = int(0.1 * total_samples)
        
        train_df = df.iloc[:train_size].copy()
        val_df = df.iloc[train_size:train_size + val_size].copy()
        test_df = df.iloc[train_size + val_size:].copy()

        # 3. 数据增强
        # 为保证训练/验证/测试三部分拥有一致的特征空间，
        # 在不泄漏目标信息的前提下（增强仅基于当前样本本身的数值列），
        # 对三份数据均执行相同的增强操作。
        train_df_augmented = self.apply_data_augmentation(train_df.copy())
        val_df_augmented = self.apply_data_augmentation(val_df.copy())
        test_df_augmented = self.apply_data_augmentation(test_df.copy())
        
        # 4. 对目标值进行标准化，使用 StandardScaler 可避免极值饱和，提升对峰值的预测能力
        target_scaler = StandardScaler()
        train_df_augmented['Charging_Load_kW'] = target_scaler.fit_transform(train_df_augmented[['Charging_Load_kW']])
        val_df_augmented['Charging_Load_kW'] = target_scaler.transform(val_df_augmented[['Charging_Load_kW']])
        test_df_augmented['Charging_Load_kW'] = target_scaler.transform(test_df_augmented[['Charging_Load_kW']])

        # 5. 特征缩放和特征选择 (Fit on augmented train, transform all)
        # Fit feature scaler and determine selected features on the augmented training data
        self.preprocessor.fit_features(train_df_augmented)
        
        # Prepare features for each split separately using the scaler fitted on training data
        # The 'prepare_features' method in DataPreprocessor will now transform using the fitted scaler
        processed_train_df = self.preprocessor.prepare_features(train_df_augmented, self.sequence_length)
        processed_val_df = self.preprocessor.prepare_features(val_df_augmented, self.sequence_length)
        processed_test_df = self.preprocessor.prepare_features(test_df_augmented, self.sequence_length)
        
        # 6. 准备序列数据 (Prepare sequences for each split separately)
        X_train, y_train = self.preprocessor.prepare_sequences(processed_train_df, self.sequence_length)
        X_val, y_val = self.preprocessor.prepare_sequences(processed_val_df, self.sequence_length)
        X_test, y_test = self.preprocessor.prepare_sequences(processed_test_df, self.sequence_length)
        
        # 记录并更新输入特征维度。每次调用都以当前训练数据为准，避免交叉验证后维度不一致
        self.input_size = X_train.shape[2]
        logging.info(f'Input feature dimension (updated): {self.input_size}')

        # 7. 创建数据加载器
        train_dataset = TensorDataset(X_train, y_train)
        val_dataset = TensorDataset(X_val, y_val)
        test_dataset = TensorDataset(X_test, y_test)
        
        # 使用 pin_memory 加速 CPU→GPU 拷贝
        train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False, pin_memory=True)
        test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False, pin_memory=True)

        return train_loader, val_loader, test_loader, target_scaler
    
    def apply_data_augmentation(self, df):
        """
        应用数据增强方法（针对新数据集优化）

        Args:
            df (pd.DataFrame): 输入数据框

        Returns:
            pd.DataFrame: 增强后的数据框
        """
        df = df.copy()

        # 1. 添加高斯噪声（针对气象数据调整噪声水平）
        def add_gaussian_noise(data, col_name, mean=0):
            if 'Temperature' in col_name:
                std = 0.5  # 温度数据噪声标准差
            elif 'Precipitation' in col_name:
                std = 0.1  # 降水量数据噪声标准差
            elif 'Charging_Load' in col_name:
                std = 0.02  # 充电负荷数据噪声标准差
            else:
                std = 0.01  # 默认噪声标准差

            noise = np.random.normal(mean, std, data.shape)
            return data + noise

        # 2. 时间窗口滑动平均
        def apply_moving_average(data, window_size=3):
            return data.rolling(window=window_size, min_periods=1).mean()

        # 3. 随机缩放（针对不同类型数据调整缩放范围）
        def random_scaling(data, col_name):
            if 'Temperature' in col_name:
                scale_range = (0.98, 1.02)  # 温度数据小幅缩放
            elif 'Precipitation' in col_name:
                scale_range = (0.9, 1.1)   # 降水量数据适中缩放
            elif 'Charging_Load' in col_name:
                scale_range = (0.95, 1.05)  # 充电负荷数据小幅缩放
            else:
                scale_range = (0.9, 1.1)   # 默认缩放范围

            scale = np.random.uniform(scale_range[0], scale_range[1])
            return data * scale

        # 对数值列应用数据增强
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != 'Timestamp':  # 跳过时间戳列
                # 添加高斯噪声
                df[f'{col}_noise'] = add_gaussian_noise(df[col].values, col)

                # 应用滑动平均
                df[f'{col}_ma'] = apply_moving_average(df[col])

                # 随机缩放
                df[f'{col}_scaled'] = random_scaling(df[col].values, col)

        return df
    
    def optimize_hyperparameters(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader
    ) -> Dict[str, float]:
        """使用贝叶斯优化算法优化超参数。"""
        # 定义超参数搜索空间
        space = [
            Integer(1, 8, name='num_layers'),          # 层数不宜过深，1~8
            Integer(64, 512, name='hidden_size'),      # 隐藏通道
            Integer(2, 12, name='kernel_size'),        # 卷积核尺寸
            Real(0.1, 0.6, name='dropout'),            # 合理的 dropout 范围
            Real(1e-4, 1e-2, 'log-uniform', name='learning_rate'),  # 学习率
            Categorical([0, 1, 2], name='loss_type'),  # 损失函数类型：0=MSE, 1=L1, 2=Huber
            Categorical([0, 1, 2], name='optimizer_type'),  # 优化器类型：0=Adam, 1=AdamW, 2=RAdam
            Real(1e-6, 1e-4, 'log-uniform', name='weight_decay')  # 权重衰减参数
        ]

        def objective_function(params):
            num_layers, hidden_size, kernel_size, dropout, learning_rate, loss_type, optimizer_type, weight_decay = params

            # 修复类型错误：将numpy类型转换为python原生int
            num_layers = int(num_layers)
            hidden_size = int(hidden_size)
            kernel_size = int(kernel_size)

            # 映射离散型参数
            loss_functions = {
                0: nn.MSELoss(),
                1: nn.L1Loss(),
                2: nn.SmoothL1Loss()
            }
            criterions = {
                0: 'MSE',
                1: 'L1',
                2: 'Huber'
            }
            
            criterion = loss_functions[loss_type]
            loss_name = criterions[loss_type]

            # 动态获取并校验实际的input_size
            actual_input_size = 0
            try:
                # 尝试从数据加载器中获取一个批次来确定input_size
                # 使用iter和next避免DataLoader为空的问题
                # 对于空数据加载器，next()会抛出StopIteration
                X_sample, _ = next(iter(train_loader))
                actual_input_size = X_sample.shape[2] # 假设形状是 (batch_size, sequence_length, input_size)
                logging.info(f"成功从train_loader中获取input_size: {actual_input_size}")
            except StopIteration:
                logging.error(f"训练数据加载器为空。参数组合: {params}")
                return 0.001 # 返回惩罚值
            except Exception as e:
                logging.error(f"获取训练数据样本input_size失败: {e}。参数组合: {params}")
                return 0.001 # 返回惩罚值

            if actual_input_size == 0:
                logging.error(f"训练数据加载器为空或输入特征维度为0。参数组合: {params}")
                return 0.001 # 返回一个大的有限值，避免skopt报错

            config = {
                'input_size': actual_input_size, # 使用动态获取的input_size
                'hidden_size': hidden_size,
                'num_layers': num_layers,
                'output_size': 1,
                'kernel_size': kernel_size,
                'dropout': dropout,
                'learning_rate': learning_rate,
                'optimizer_type': optimizer_type,
                'weight_decay': weight_decay
            }
            
            logging.info(f"""
            当前参数组合:
            num_layers: {num_layers}, hidden_size: {hidden_size}, kernel_size: {kernel_size},
            dropout: {dropout}, learning_rate: {learning_rate}, loss_type: {loss_name},
            optimizer_type: {optimizer_type}, weight_decay: {weight_decay},
            实际input_size: {actual_input_size}
            """
            )

            try:
                model = create_model(config).to(self.device)
                
                if optimizer_type == 0:
                    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
                elif optimizer_type == 1:
                    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
                elif optimizer_type == 2:
                    # 检查是否安装了 RAdam，否则回退到 Adam
                    try:
                        from torch_optimizer import RAdam
                        optimizer = RAdam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
                    except ImportError:
                        logging.warning("RAdam not found, falling back to Adam.")
                        optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
                else:
                    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay) # 默认 Adam

                # ---------- Quick training (few epochs) ---------- #
                quick_epochs = 3
                for _ in range(quick_epochs):
                    model.train()
                    for X_batch, y_batch in train_loader:
                        X_batch = X_batch.to(self.device, non_blocking=True)
                        y_batch = y_batch.to(self.device, non_blocking=True)
                        optimizer.zero_grad()
                        pred = model(X_batch)
                        # 使用 SMAPE 避免分母过小导致梯度爆炸
                        smape_loss = torch.mean(
                            torch.abs(pred - y_batch)
                            / (torch.clamp(torch.abs(pred) + torch.abs(y_batch), min=1e-3) / 2)
                        )
                        loss = 0.1 * smape_loss
                        loss.backward()
                        optimizer.step()

                # ---------- Evaluate on validation ---------- #
                model.eval()
                val_loss = 0
                with torch.no_grad():
                    for X_val, y_val in val_loader:
                        X_val = X_val.to(self.device, non_blocking=True)
                        y_val = y_val.to(self.device, non_blocking=True)
                        pred_val = model(X_val)
                        val_base = criterion(pred_val, y_val)
                        val_smape = torch.mean(
                            torch.abs(pred_val - y_val)
                            / (torch.clamp(torch.abs(pred_val) + torch.abs(y_val), min=1e-3) / 2)
                        )
                        val_loss += (val_base + 0.1 * val_smape).item()
                avg_val_loss = val_loss / max(1, len(val_loader))
                logging.info(f"验证损失: {avg_val_loss:.6f}")
                return avg_val_loss

            except Exception as e:
                logging.error(f"参数组合 {params} 导致错误: {e}")
                return 0.001 # 返回一个大的有限值，表示该组合不可行

        logging.info("开始贝叶斯优化...")
        # 强制每次都重新搜索，不加载旧参数
        self.best_params = None
        
        # 执行贝叶斯优化
        with tqdm(total=100, desc="贝叶斯优化进度") as pbar:
            # 定义一个回调函数，每次迭代后更新进度条
            def callback(res):
                pbar.update(1)

            res = gp_minimize(
                objective_function,
                space,
                n_calls=100,  # 迭代次数
                random_state=42,
                verbose=False, # 关闭gp_minimize的详细输出，由tqdm控制
                n_jobs=-1, # 使用所有可用核心
                callback=callback # 添加回调函数
            )

        best_hyperparameters = {
            'num_layers': int(res.x[0]),
            'hidden_size': int(res.x[1]),
            'kernel_size': int(res.x[2]),
            'dropout': float(res.x[3]),
            'learning_rate': float(res.x[4]),
            'loss_type': int(res.x[5]),
            'optimizer_type': int(res.x[6]),
            'weight_decay': float(res.x[7])
        }
        
        # 将损失函数类型和优化器类型映射回可读的名称
        loss_type_name = {0: 'MSELoss', 1: 'L1Loss', 2: 'SmoothL1Loss'}[best_hyperparameters['loss_type']]
        optimizer_type_name = {0: 'Adam', 1: 'AdamW', 2: 'RAdam'}[best_hyperparameters['optimizer_type']]
        
        best_hyperparameters['loss_type'] = loss_type_name
        best_hyperparameters['optimizer_type'] = optimizer_type_name
        
        self.best_params = best_hyperparameters
        save_best_params(self.best_params, self.output_dir)
        logging.info(f"最优超参数已保存至: {os.path.join(self.output_dir, 'best_params.json')}")
        logging.info(f"最佳验证损失: {res.fun:.4f}")

        return self.best_params

    def optimize_hyperparameters_advanced(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        n_calls: int = 150,
        enable_multi_objective: bool = True,
        enable_uncertainty_quantification: bool = True
    ) -> Dict[str, float]:
        """
        使用增强贝叶斯优化算法优化超参数

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            n_calls: 优化调用次数
            enable_multi_objective: 是否启用多目标优化
            enable_uncertainty_quantification: 是否启用不确定性量化

        Returns:
            最优超参数字典
        """
        logging.info("开始增强贝叶斯优化...")

        # 创建增强贝叶斯优化器
        optimizer = AdvancedBayesianOptimizer(
            output_dir=self.output_dir,
            n_calls=n_calls,
            n_initial_points=max(20, n_calls // 8),  # 自适应初始点数
            acquisition_func='EI',  # 使用期望改进
            enable_uncertainty_quantification=enable_uncertainty_quantification,
            enable_multi_objective=enable_multi_objective,
            enable_warm_start=True,  # 启用温启动
            random_state=42
        )

        # 执行优化
        best_params = optimizer.optimize(
            train_loader=train_loader,
            val_loader=val_loader,
            create_model_func=create_model,
            device=self.device
        )

        # 保存最优参数
        self.best_params = best_params
        save_best_params(self.best_params, self.output_dir)

        logging.info("增强贝叶斯优化完成")
        logging.info(f"最优参数: {best_params}")

        return self.best_params

    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        config: Dict[str, any],
        scaler: MinMaxScaler,
        fold_idx: Optional[int] = None
    ) -> Tuple[nn.Module, str, float]:
        """训练模型"""
        # 根据config中的字符串名称选择损失函数
        loss_name = config.get('loss_type', 'MSELoss')
        if loss_name == 'MSELoss':
            criterion = nn.MSELoss()
        elif loss_name == 'L1Loss':
            criterion = nn.L1Loss()
        elif loss_name == 'SmoothL1Loss':
            criterion = nn.SmoothL1Loss()
        else:
            logging.warning(f"未知损失函数类型: {loss_name}，默认使用MSELoss。")
            criterion = nn.MSELoss()
            
        model = create_model(config).to(self.device)
        
        # 从配置中选择优化器
        optimizer_class = {
            0: optim.Adam,
            1: optim.AdamW,
            2: optim.RAdam
        }.get(config.get('optimizer_type', 0), optim.Adam)
        
        optimizer = optimizer_class(model.parameters(), lr=config['learning_rate'], weight_decay=config.get('weight_decay', 1e-5))

        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 'min', patience=5, factor=0.5
        )

        # 早停机制
        early_stopping = EarlyStopping(patience=15, min_delta=1e-4)

        history = {'train_loss': [], 'val_loss': [], 'learning_rates': []}
        best_val_loss = float('inf')
        fold_model_path = None  # 跟踪当前折的模型文件路径

        for epoch in range(400):
            model.train()
            train_loss = 0.0
            
            # 使用tqdm创建训练进度条
            train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{400} [训练]', leave=False)
            
            for X, y in train_pbar:
                X, y = X.to(self.device, non_blocking=True), y.to(self.device, non_blocking=True)
                
                optimizer.zero_grad()
                output = model(X)
                
                # --- 复合损失函数 ---
                base_loss = criterion(output, y)
                smape_loss = torch.mean(
                    2 * torch.abs(output - y) / 
                    torch.clamp(torch.abs(output) + torch.abs(y), min=1e-4)
                )
                loss = base_loss + 0.1 * smape_loss
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0) # 梯度裁剪
                optimizer.step()
                
                train_loss += loss.item()
                train_pbar.set_postfix(loss=f'{loss.item():.4f}')
            
            avg_train_loss = train_loss / len(train_loader)
            history['train_loss'].append(avg_train_loss)

            model.eval()
            val_loss = 0.0
            
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/400 [验证]', leave=False)
            
            with torch.no_grad():
                for X, y in val_pbar:
                    X, y = X.to(self.device, non_blocking=True), y.to(self.device, non_blocking=True)
                    output = model(X)
                    
                    # --- 在验证集上也使用相同的复合损失 ---
                    base_loss = criterion(output, y)
                    smape_loss = torch.mean(
                        2 * torch.abs(output - y) / 
                        torch.clamp(torch.abs(output) + torch.abs(y), min=1e-4)
                    )
                    loss = base_loss + 0.1 * smape_loss
                    
                    val_loss += loss.item()
                    val_pbar.set_postfix(val_loss=f'{loss.item():.4f}')

            avg_val_loss = val_loss / len(val_loader)
            history['val_loss'].append(avg_val_loss)
            history['learning_rates'].append(optimizer.param_groups[0]['lr'])

            epoch_summary = f'Epoch {epoch+1}/400 | Train Loss: {avg_train_loss:.4f} | Val Loss: {avg_val_loss:.4f} | LR: {optimizer.param_groups[0]["lr"]:.6f}'
            
            # 更新epoch进度条
            train_pbar.set_postfix({
                'train_loss': f'{avg_train_loss:.4f}',
                'val_loss': f'{avg_val_loss:.4f}',
                'lr': f'{optimizer.param_groups[0]["lr"]:.6f}'
            })
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                best_model = model.state_dict().copy()

                # 删除旧模型文件（如果存在）
                if fold_model_path and os.path.exists(fold_model_path):
                    os.remove(fold_model_path)
                    logging.info(f"已删除旧模型文件: {fold_model_path}")

                # 保存新模型文件（每折只保留一个文件）
                if fold_idx is not None:
                    model_filename = f'best_model_fold{fold_idx}.pth'
                else:
                    model_filename = f'best_model_final.pth'
                fold_model_path = os.path.join(self.output_dir, model_filename)
                torch.save(best_model, fold_model_path)
                logging.info(f"最佳模型已保存至: {fold_model_path}")
            
            if early_stopping.check_early_stop(avg_val_loss):
                logging.info(f'Early stopping triggered at epoch {epoch+1}')
                break
        
        # Load the best model found during training
        model.load_state_dict(best_model)

        # 绘制训练历史图
        self.plot_training_history(history, scaler) # Plot after training loop finishes

        # Note: Feature importance and final evaluation will be done in main function
        # after training is complete and best model is loaded.

        return model, fold_model_path, best_val_loss
    
    def evaluate(
        self,
        model: nn.Module,
        test_loader: DataLoader,
        scaler: MinMaxScaler
    ) -> Tuple[float, float, float, float, float, float, np.ndarray, np.ndarray]:
        """评估模型"""
        model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for X, y in test_loader:
                X = X.to(self.device, non_blocking=True)
                y = y.to(self.device, non_blocking=True)
                output = model(X)
                predictions.extend(output.cpu().numpy())
                actuals.extend(y.cpu().numpy())
        
        # 反归一化以计算指标
        predictions_scaled = np.array(predictions).reshape(-1, 1)
        actuals_scaled = np.array(actuals).reshape(-1, 1)
        predictions_orig = scaler.inverse_transform(predictions_scaled)
        actuals_orig = scaler.inverse_transform(actuals_scaled)

        # 从反归一化的真实值和预测值计算所有指标
        mse = np.mean((predictions_orig - actuals_orig) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(predictions_orig - actuals_orig))

        # 强化 MAPE 计算
        load_thresh = 10.0
        high_load_mask = np.abs(actuals_orig) >= load_thresh
        if np.any(high_load_mask):
            denom = np.maximum(np.abs(actuals_orig[high_load_mask]), load_thresh)
            mape_array = np.abs((actuals_orig[high_load_mask] - predictions_orig[high_load_mask]) / denom)
            mape_percent = np.mean(mape_array) * 100
        else:
            mape_percent = 0.0

        # Accuracy 定义为 100 - MAPE
        accuracy_percent = 100 - mape_percent
        
        # 计算R2分数
        r2 = self.calculate_r2(predictions_orig, actuals_orig)
        
        logging.info(f'Test MSE: {mse:.4f}')
        logging.info(f'Test RMSE: {rmse:.4f}')
        logging.info(f'Test MAE: {mae:.4f}')
        logging.info(f'Test MAPE: {mape_percent:.2f}%')
        logging.info(f'Test Accuracy: {accuracy_percent:.2f}%')
        logging.info(f'Test R2: {r2:.4f}')
        
        return (
            mse,
            rmse,
            mae,
            mape_percent,
            accuracy_percent,
            r2,
            predictions_orig,
            actuals_orig,
        )

    def plot_training_history(self, history, scaler):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))
        
        # 绘制损失
        ax1.plot(history['train_loss'], label='训练损失')
        ax1.plot(history['val_loss'], label='验证损失')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('损失')
        ax1.legend()
        ax1.grid(True)
        
        # 绘制学习率变化
        ax2.plot(history['learning_rates'], label='学习率', color='orange')
        ax2.set_title('学习率变化')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('学习率')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        save_plot(fig, 'training_history', self.output_dir)
    
    def plot_feature_importance(self, feature_importance):
        """绘制特征重要性"""
        fig, ax = plt.subplots(figsize=(10, 6))
        feature_importance.plot(kind='bar', ax=ax)
        ax.set_title('特征重要性')
        ax.set_xlabel('特征')
        ax.set_ylabel('重要性得分')
        plt.xticks(rotation=45)
        plt.tight_layout()
        save_plot(fig, 'feature_importance', self.output_dir)
    
    def save_results(self, mse: float, rmse: float, mae: float, mape: float, accuracy: float, r2: float, predictions_orig: np.ndarray, actuals_orig: np.ndarray):
        """保存评估结果和预测结果"""
        # 保存预测结果
        results_df = pd.DataFrame({
            '预测值': predictions_orig.flatten(),
            '实际值': actuals_orig.flatten()
        })
        save_table(results_df, 'prediction_results', self.output_dir)
        
        # 保存评估指标
        metrics_df = pd.DataFrame({
            '指标': ['MSE', 'RMSE', 'MAE', 'MAPE(%)', 'Accuracy(%)', 'R2'],
            '值': [
                mse,
                rmse,
                mae,
                mape,
                accuracy,
                r2
            ]
        })
        save_table(metrics_df, 'evaluation_metrics', self.output_dir)

    def calculate_feature_importance(self, model, val_loader):
        """计算特征重要性"""
        model.eval()
        feature_importance = {}

        # 获取当前损失函数类型
        loss_type = getattr(self, 'best_params', {}).get('loss_type', 0)
        if loss_type == 0:
            criterion = nn.MSELoss()
        elif loss_type == 1:
            criterion = nn.L1Loss()
        else:
            criterion = nn.SmoothL1Loss()

        # 动态获取实际特征名
        if hasattr(self.preprocessor, 'selected_features') and self.preprocessor.selected_features:
            feature_names = self.preprocessor.selected_features
        else:
            feature_names = [f'feature_{i}' for i in range(self.input_size)]

        with torch.no_grad():
            for X, y in val_loader:
                X = X.to(self.device, non_blocking=True)
                y = y.to(self.device, non_blocking=True)
                baseline_output = model(X)
                baseline_loss = criterion(baseline_output, y)

                for i, feature_name in enumerate(feature_names):
                    if i >= X.shape[2]:
                        continue  # 跳过不存在的特征
                    original_values = X[:, :, i].clone()
                    X[:, :, i] = torch.randn_like(X[:, :, i])
                    perturbed_output = model(X)
                    perturbed_loss = criterion(perturbed_output, y)
                    importance = (perturbed_loss - baseline_loss).item()
                    if feature_name not in feature_importance:
                        feature_importance[feature_name] = []
                    feature_importance[feature_name].append(importance)
                    X[:, :, i] = original_values

        avg_importance = {name: np.mean(scores) for name, scores in feature_importance.items()}
        importance_df = pd.DataFrame({
            'Feature': list(avg_importance.keys()),
            'Importance': list(avg_importance.values())
        })
        importance_df = importance_df.sort_values('Importance', ascending=False)
        return importance_df

    def calculate_metrics(self, predictions, actuals):
        """计算评估指标"""
        predictions = np.array(predictions)
        actuals = np.array(actuals)
        
        # 计算MSE
        mse = np.mean((predictions - actuals) ** 2)
        
        # 计算RMSE
        rmse = np.sqrt(mse)
        
        # 计算MAE
        mae = np.mean(np.abs(predictions - actuals))
        
        return mse, rmse, mae
    
    def calculate_r2(self, predictions, actuals):
        """计算R2分数"""
        predictions = np.array(predictions)
        actuals = np.array(actuals)
        
        # 计算总平方和
        ss_tot = np.sum((actuals - np.mean(actuals)) ** 2)
        
        # 计算残差平方和
        ss_res = np.sum((actuals - predictions) ** 2)
        
        # 计算R2
        r2 = 1 - (ss_res / ss_tot)
        
        return r2

    def select_best_global_model(self, fold_models: List[Dict]) -> Dict:
        """从所有折中选择全局最佳模型

        Args:
            fold_models (List[Dict]): 包含每折模型信息的列表

        Returns:
            Dict: 最佳模型的信息
        """
        # 策略1：选择验证损失最低的模型
        best_model = min(fold_models, key=lambda x: x['val_loss'])

        # 策略2：综合考虑多个指标（可选）
        # best_model = min(fold_models, key=lambda x: x['val_loss'] * (1 - x['r2']))

        logging.info(f"选择第{best_model['fold']}折作为全局最佳模型")
        logging.info(f"最佳验证损失: {best_model['val_loss']:.6f}")
        logging.info(f"最佳模型R2: {best_model['r2']:.6f}")
        logging.info(f"最佳模型MAPE: {best_model['mape']:.2f}%")

        return best_model

    def cleanup_redundant_models(self, fold_models: List[Dict], best_model_path: str):
        """清理冗余模型文件，只保留最佳模型

        Args:
            fold_models (List[Dict]): 包含每折模型信息的列表
            best_model_path (str): 最佳模型的文件路径
        """
        cleaned_count = 0
        for model_info in fold_models:
            model_path = model_info['model_path']
            if model_path != best_model_path and os.path.exists(model_path):
                os.remove(model_path)
                logging.info(f"已删除冗余模型: {os.path.basename(model_path)}")
                cleaned_count += 1

        logging.info(f"共清理了 {cleaned_count} 个冗余模型文件")

    def save_final_model(self, best_model_path: str, config: Dict) -> str:
        """保存最终部署模型

        Args:
            best_model_path (str): 最佳模型的文件路径
            config (Dict): 模型配置

        Returns:
            str: 最终模型的文件路径
        """
        final_model_path = os.path.join(self.output_dir, 'final_best_model.pth')

        # 复制最佳模型
        import shutil
        shutil.copy2(best_model_path, final_model_path)

        # 保存模型配置
        model_config = {
            'config': config,
            'model_path': final_model_path,
            'source_model': best_model_path,
            'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
            'model_type': 'IBOTCNGRU',
            'description': '十折交叉验证选出的全局最佳模型'
        }

        config_path = os.path.join(self.output_dir, 'final_model_config.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(model_config, f, indent=4, ensure_ascii=False)

        logging.info(f"最终部署模型已保存至: {final_model_path}")
        logging.info(f"模型配置已保存至: {config_path}")
        return final_model_path

    def cross_validate(self, file_path: str, n_splits: int = 10, params: Optional[Dict] = None):
        """执行交叉验证
        
        Args:
            file_path (str): 数据文件路径.
            n_splits (int, optional): 交叉验证折数. Defaults to 10.
            params (Optional[Dict], optional): 如果提供，则使用此参数配置，跳过超参数优化. Defaults to None.
        """
        logging.info(f"开始 {n_splits} 折交叉验证...")

        # 1. 仅加载、清洗和填充缺失值一次
        df_raw = self.preprocessor.load_and_clean_data(file_path)
        df_raw = self.preprocessor.fill_missing_values(df_raw)
        df_raw = df_raw.sort_values('Timestamp').reset_index(drop=True)

        # 2. 创建K折交叉验证 (使用TimeSeriesSplit)
        kf = TimeSeriesSplit(n_splits=n_splits)
        
        # 存储每折的结果
        fold_results = []
        fold_models = []  # 存储每折的模型信息
        
        # 使用tqdm创建交叉验证的进度条
        for fold, (train_idx, val_idx) in enumerate(tqdm(kf.split(df_raw), total=n_splits, desc="交叉验证进度")):
            logging.info(f'\n开始第 {fold + 1} 折交叉验证')
            
            # 3. 按索引切分原始数据帧
            train_df_fold = df_raw.iloc[train_idx].copy()
            val_df_fold = df_raw.iloc[val_idx].copy()

            # 4. 数据增强
            # 为保证训练/验证/测试三部分拥有一致的特征空间，
            # 在不泄漏目标信息的前提下（增强仅基于当前样本本身的数值列），
            # 对三份数据均执行相同的增强操作。
            train_df_fold_augmented = self.apply_data_augmentation(train_df_fold.copy())
            val_df_fold_augmented = self.apply_data_augmentation(val_df_fold.copy())

            # 5. 目标值归一化 (为当前折叠创建新的scaler)
            fold_target_scaler = StandardScaler()
            train_df_fold_augmented['Charging_Load_kW'] = fold_target_scaler.fit_transform(train_df_fold_augmented[['Charging_Load_kW']])
            val_df_fold_augmented['Charging_Load_kW'] = fold_target_scaler.transform(val_df_fold_augmented[['Charging_Load_kW']])

            # 6. 特征缩放和特征选择 (为当前折叠创建新的preprocessor实例)
            # 这确保了特征缩放器和选择器仅在当前训练数据上拟合
            fold_preprocessor = DataPreprocessor(scaler_type=self.preprocessor.scaler_type,
                                                 k_best_features=self.preprocessor.k_best_features)
            
            # 在增强后的训练数据上拟合特征缩放器和选择器
            fold_preprocessor.fit_features(train_df_fold_augmented)
            
            # 使用拟合好的preprocessor处理当前折叠的训练和验证数据
            processed_train_df = fold_preprocessor.prepare_features(train_df_fold_augmented, self.sequence_length)
            processed_val_df = fold_preprocessor.prepare_features(val_df_fold_augmented, self.sequence_length)
            
            # 记录当前折叠的输入特征维度
            # 确保selected_features在fold_preprocessor中被正确设置
            current_input_size = processed_train_df.drop(columns=['Timestamp', 'target']).shape[1]
            if fold == 0:  # 仅日志记录本折叠的输入特征维度，不再修改全局 input_size，避免后续维度不一致
                logging.info(f'Input feature dimension for fold {fold + 1}: {current_input_size}')

            # 7. 准备序列数据
            X_train, y_train = fold_preprocessor.prepare_sequences(processed_train_df, self.sequence_length)
            X_val, y_val = fold_preprocessor.prepare_sequences(processed_val_df, self.sequence_length)
            
            # 8. 创建数据加载器
            train_dataset = TensorDataset(X_train, y_train)
            val_dataset = TensorDataset(X_val, y_val)
            
            train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True, pin_memory=True)
            val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False, pin_memory=True)

            # NEW: Check if DataLoaders are empty before proceeding
            if len(train_dataset) == 0 or len(val_dataset) == 0:
                logging.warning(f"折叠 {fold + 1}：训练集或验证集为空，跳过此折叠。")
                fold_results.append({
                    'fold': fold + 1,
                    'mse': 1.0, # Record a high penalty for empty folds
                    'rmse': 1.0,
                    'mae': 1.0,
                    'mape': 1.0,
                    'accuracy': 0.0,
                    'r2': -1.0, # Very bad R2
                    'params': {}
                })
                continue # Skip to the next fold

            # 如果没有预设参数，则执行超参数优化
            if params is None:
                logging.info(f"折叠 {fold + 1}：开始超参数优化...")
                best_params = self.optimize_hyperparameters(train_loader, val_loader)
                logging.info(f"折叠 {fold + 1}：最优参数: {best_params}")
            else:
                logging.info(f"折叠 {fold + 1}：使用预设参数进行训练...")
                best_params = convert_params_types(params)

            config = {
                'input_size': current_input_size, # 使用当前折叠的input_size
                'hidden_size': best_params['hidden_size'],
                'num_layers': best_params['num_layers'],
                'output_size': 1,
                'kernel_size': best_params['kernel_size'],
                'dropout': best_params['dropout'],
                'learning_rate': best_params['learning_rate'],
                'loss_type': best_params['loss_type'],
                'optimizer_type': best_params['optimizer_type'],
                'weight_decay': best_params['weight_decay']
            }
            
            # 创建新的模型实例以避免参数共享
            model = create_model(config).to(self.device)

            trained_model, model_path, val_loss = self.train(train_loader, val_loader, config, fold_target_scaler, fold_idx=fold + 1)

            mse, rmse, mae, mape, accuracy, r2, predictions_orig, actuals_orig = self.evaluate(trained_model, val_loader, fold_target_scaler)  # 评估使用val_loader
            
            # 绘制每一折的散点图和误差分布图
            self.plot_scatter_and_error(predictions_orig, actuals_orig, fold + 1)

            # 记录结果
            fold_results.append({
                'fold': fold + 1,
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'mape': mape,
                'accuracy': accuracy,
                'r2': r2,
                'val_loss': val_loss,
                'model_path': model_path,
                'params': best_params
            })

            # 记录模型信息用于后续选择
            fold_models.append({
                'fold': fold + 1,
                'model_path': model_path,
                'val_loss': val_loss,
                'mse': mse,
                'mape': mape,
                'r2': r2
            })
        
        # 计算平均结果
        avg_results = {
            'mse': np.mean([r['mse'] for r in fold_results]),
            'rmse': np.mean([r['rmse'] for r in fold_results]),
            'mae': np.mean([r['mae'] for r in fold_results]),
            'mape': np.mean([r['mape'] for r in fold_results]),
            'accuracy': np.mean([r['accuracy'] for r in fold_results]),
            'r2': np.mean([r['r2'] for r in fold_results])
        }
        
        logging.info('交叉验证平均结果:')
        logging.info(f'平均 MSE: {avg_results["mse"]:.6f}')
        logging.info(f'平均 RMSE: {avg_results["rmse"]:.6f}')
        logging.info(f'平均 MAE: {avg_results["mae"]:.6f}')
        logging.info(f'平均 MAPE(%): {avg_results["mape"]:.2f}')
        logging.info(f'平均 Accuracy(%): {avg_results["accuracy"]:.2f}')
        logging.info(f'平均 R2: {avg_results["r2"]:.6f}')
        
        # 选择全局最佳模型
        logging.info('\n开始选择全局最佳模型...')
        best_model_info = self.select_best_global_model(fold_models)

        # 清理冗余模型文件
        logging.info('\n开始清理冗余模型文件...')
        self.cleanup_redundant_models(fold_models, best_model_info['model_path'])

        # 保存交叉验证结果
        cv_results_df = pd.DataFrame(fold_results)
        save_table(cv_results_df, 'cross_validation_results', self.output_dir)

        return avg_results, fold_results, best_model_info

    def plot_scatter_and_error(self, predictions_orig, actuals_orig, fold_idx: int):
        """绘制预测-实际散点图及误差分布图

        Args:
            predictions_orig (np.ndarray): 反归一化后的预测值
            actuals_orig (np.ndarray): 反归一化后的真实值
            fold_idx (int): 当前折数（从 1 开始）
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # ---- 散点图 ---- #
        ax1.scatter(actuals_orig, predictions_orig, alpha=0.5, edgecolors='b')
        min_val = min(actuals_orig.min(), predictions_orig.min())
        max_val = max(actuals_orig.max(), predictions_orig.max())
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--')
        ax1.set_title(f'Fold {fold_idx} 预测值 vs 实际值')
        ax1.set_xlabel('实际值')
        ax1.set_ylabel('预测值')
        ax1.grid(True)

        # ---- 误差分布 ---- #
        errors = (predictions_orig - actuals_orig).flatten()
        sns.histplot(errors, kde=True, bins=30, ax=ax2, color='g', edgecolor='black')
        ax2.set_title(f'Fold {fold_idx} 预测误差分布')
        ax2.set_xlabel('误差 (预测 - 实际)')
        ax2.set_ylabel('频数')
        ax2.grid(True)

        plt.tight_layout()
        save_plot(fig, f'accuracy_fold{fold_idx}', self.output_dir)

    def plot_test_evaluation(self, predictions_orig, actuals_orig):
        """绘制测试集整体评估图（曲线对比 + 散点 + 误差分布）。

        Args:
            predictions_orig (np.ndarray): 反归一化后的预测值
            actuals_orig (np.ndarray): 反归一化后的真实值
        """
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 18))

        # ---- 曲线对比 ---- #
        ax1.plot(actuals_orig, label='实际值')
        ax1.plot(predictions_orig, label='预测值')
        ax1.set_title('测试集预测结果对比')
        ax1.set_xlabel('样本')
        ax1.set_ylabel('充电负荷')
        ax1.legend()
        ax1.grid(True)

        # ---- 散点图 ---- #
        ax2.scatter(actuals_orig, predictions_orig, alpha=0.5, edgecolors='b')
        min_val = min(actuals_orig.min(), predictions_orig.min())
        max_val = max(actuals_orig.max(), predictions_orig.max())
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--')
        ax2.set_title('测试集 预测值 vs 实际值')
        ax2.set_xlabel('实际值')
        ax2.set_ylabel('预测值')
        ax2.grid(True)

        # ---- 误差分布 ---- #
        errors = (predictions_orig - actuals_orig).flatten()
        sns.histplot(errors, kde=True, bins=30, ax=ax3, color='g', edgecolor='black')
        ax3.set_title('测试集预测误差分布')
        ax3.set_xlabel('误差 (预测 - 实际)')
        ax3.set_ylabel('频数')
        ax3.grid(True)

        plt.tight_layout()
        save_plot(fig, 'test_evaluation', self.output_dir)

    def plot_test_history(self, predictions_orig, actuals_orig):
        """绘制测试集预测曲线历史（与训练历史风格一致）。

        Args:
            predictions_orig (np.ndarray): 反归一化后的预测值
            actuals_orig (np.ndarray): 反归一化后的真实值
        """
        # 仅绘制两条曲线：预测值和实际值，保持训练历史第二张子图的视觉风格
        fig, ax = plt.subplots(figsize=(10, 5))
        ax.plot(predictions_orig, label='预测值')
        ax.plot(actuals_orig, label='实际值')
        ax.set_title('测试集预测结果对比')
        ax.set_xlabel('样本')
        ax.set_ylabel('充电负荷')
        ax.legend()
        ax.grid(True)
        plt.tight_layout()
        save_plot(fig, 'test_history', self.output_dir)

    def plot_test_scatter_and_error(self, predictions_orig, actuals_orig):
        """绘制测试集散点 + 误差分布图（与交叉验证 fold 图一致的布局）。

        Args:
            predictions_orig (np.ndarray): 反归一化后的预测值
            actuals_orig (np.ndarray): 反归一化后的真实值
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # ---- 散点图 ---- #
        ax1.scatter(actuals_orig, predictions_orig, alpha=0.5, edgecolors='b')
        min_val = min(actuals_orig.min(), predictions_orig.min())
        max_val = max(actuals_orig.max(), predictions_orig.max())
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--')
        ax1.set_title('测试集 预测值 vs 实际值')
        ax1.set_xlabel('实际值')
        ax1.set_ylabel('预测值')
        ax1.grid(True)

        # ---- 误差分布 ---- #
        errors = (predictions_orig - actuals_orig).flatten()
        sns.histplot(errors, kde=True, bins=30, ax=ax2, color='g', edgecolor='black')
        ax2.set_title('测试集 预测误差分布')
        ax2.set_xlabel('误差 (预测 - 实际)')
        ax2.set_ylabel('频数')
        ax2.grid(True)

        plt.tight_layout()
        save_plot(fig, 'test_accuracy', self.output_dir)

class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=10, min_delta=0):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = None
        self.should_stop = False
        
    def check_early_stop(self, val_loss):
        if self.best_loss is None:
            self.best_loss = val_loss
        elif val_loss > self.best_loss - self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.should_stop = True
        else:
            self.best_loss = val_loss
            self.counter = 0
        return self.should_stop

def main():
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 根据是否存在 GPU 自动选择设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logging.info(f"使用设备: {device}")

    # 初始化预测器
    predictor = EVChargingPredictor(device=device)
    
    # --- 工作流程重构 ---
    # 1. 准备一次性数据用于超参数优化
    logging.info("准备数据用于超参数优化...")
    train_loader_for_hpo, val_loader_for_hpo, _, _ = predictor.prepare_data('ev_charging_data.csv')

    # 2. 执行增强贝叶斯优化，找到最佳参数组合
    logging.info("开始增强贝叶斯优化...")
    best_params = predictor.optimize_hyperparameters_advanced(
        train_loader_for_hpo,
        val_loader_for_hpo,
        n_calls=150,  # 增加优化调用次数
        enable_multi_objective=True,  # 启用多目标优化
        enable_uncertainty_quantification=True  # 启用不确定性量化
    )
    logging.info(f"增强优化最优参数已找到: {best_params}")

    # 确保参数类型正确
    best_params = convert_params_types(best_params)
    save_best_params(best_params, predictor.output_dir) # 保存最优参数

    # 3. 使用找到的最佳参数执行交叉验证，以稳健地评估模型性能
    logging.info("使用最优参数进行交叉验证...")
    avg_results, fold_results, best_model_info = predictor.cross_validate(
        'ev_charging_data.csv',
        n_splits=10,
        params=best_params # 传入最优参数
    )
    
    # 4. 保存最终部署模型
    logging.info("保存最终部署模型...")
    config = {
        'input_size': predictor.input_size,
        'hidden_size': best_params['hidden_size'],
        'num_layers': best_params['num_layers'],
        'output_size': 1,
        'kernel_size': best_params['kernel_size'],
        'dropout': best_params['dropout'],
        'learning_rate': best_params['learning_rate'],
        'loss_type': best_params['loss_type'],
        'optimizer_type': best_params['optimizer_type'],
        'weight_decay': best_params['weight_decay']
    }

    final_model_path = predictor.save_final_model(best_model_info['model_path'], config)
    
    # 5. 在测试集上评估最佳模型
    logging.info("在测试集上评估最佳模型...")

    # 加载最佳模型进行最终评估
    best_model = create_model(config).to(predictor.device)
    best_model.load_state_dict(torch.load(best_model_info['model_path']))

    # 准备测试数据
    _, _, test_loader, target_scaler = predictor.prepare_data('ev_charging_data.csv')

    # 评估最佳模型
    mse, rmse, mae, mape, accuracy, r2, predictions_orig, actuals_orig = predictor.evaluate(
        best_model, test_loader, target_scaler
    )
    
    # 保存评估结果和预测结果
    predictor.save_results(mse, rmse, mae, mape, accuracy, r2, predictions_orig, actuals_orig)

    # 计算并绘制特征重要性
    feature_importance_df = predictor.calculate_feature_importance(best_model, test_loader)
    predictor.plot_feature_importance(feature_importance_df)

    # 绘制测试集预测曲线历史
    predictor.plot_test_history(predictions_orig, actuals_orig)

    # 测试集散点 + 误差分布
    predictor.plot_test_scatter_and_error(predictions_orig, actuals_orig)

    # 绘制测试集整体评估图
    predictor.plot_test_evaluation(predictions_orig, actuals_orig)

    # 生成增强贝叶斯优化报告
    logging.info("生成增强贝叶斯优化报告...")
    try:
        create_optimization_report(predictor.output_dir)
        logging.info("优化报告生成完成")
        print("\n增强贝叶斯优化报告已生成，请查看输出目录中的可视化图表")
    except Exception as e:
        logging.warning(f"优化报告生成失败: {e}")

    logging.info("训练和评估完成")
    print("\n" + "="*50)
    print("训练和评估完成!")
    print("="*50)

if __name__ == '__main__':
    main()