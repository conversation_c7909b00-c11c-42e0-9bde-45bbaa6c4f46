#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI后端服务 - 简化的后端接口
"""

import os
import torch
import pandas as pd
import numpy as np
import psutil
import json
from datetime import datetime
from typing import Dict, Any, Callable, Optional
from pathlib import Path

# 导入统一的配置和日志系统
from config import get_config, ConfigManager
from logger import get_logger, LogCapture, ProgressLogger

# 导入核心模块
try:
    from train import EVChargingPredictor
    from data_preprocessing import DataPreprocessor
    from model import create_model
except ImportError as e:
    print(f"导入模块失败: {e}")
    raise

class PredictionBackend:
    """简化的预测系统后端服务"""

    def __init__(self):
        """初始化后端服务"""
        # 使用统一的配置和日志系统
        self.config_manager = get_config()
        self.logger = get_logger('backend')
        self.log_capture = LogCapture('backend')

        # 核心组件
        self.predictor = None
        self.data_preprocessor = None
        self.current_data = None
        self.trained_model = None
        self.training_history = None
        self.stop_flag = False

        # 设备配置
        self.device = self._get_device()

        self.logger.info("后端服务初始化完成")

    def _get_device(self) -> str:
        """获取计算设备"""
        if self.config_manager.system.enable_gpu and torch.cuda.is_available():
            return 'cuda'
        return 'cpu'

    def _update_configs(self, updates: Dict[str, Any]):
        """更新配置"""
        for section, values in updates.items():
            if section == 'model':
                self.config_manager.update_model_config(**values)
            elif section == 'training':
                self.config_manager.update_training_config(**values)
            elif section == 'optimization':
                self.config_manager.update_optimization_config(**values)
            elif section == 'data':
                self.config_manager.update_data_config(**values)
            elif section == 'system':
                self.config_manager.update_system_config(**values)
        
    def load_data(self, file_path: str) -> pd.DataFrame:
        """加载数据文件"""
        try:
            self.logger.info(f"加载数据文件: {file_path}")

            # 使用配置中的数据设置
            self.config_manager.update_data_config(data_file=file_path)

            # 初始化数据预处理器
            if not self.data_preprocessor:
                self.data_preprocessor = DataPreprocessor()

            df = self.data_preprocessor.load_and_clean_data(file_path)
            df = self.data_preprocessor.fill_missing_values(df)

            self.current_data = df
            self.logger.info(f"数据加载成功: {df.shape[0]} 行 × {df.shape[1]} 列")

            return df

        except Exception as e:
            self.logger.error(f"数据加载失败: {str(e)}")
            raise Exception(f"数据加载失败: {str(e)}")

    def validate_data(self, file_path: str) -> Dict[str, Any]:
        """验证数据格式"""
        try:
            df = self.load_data(file_path)

            # 从配置获取必要列
            data_config = self.config_manager.get_data_config()
            required_columns = [
                data_config['timestamp_column'],
                data_config['target_column']
            ]

            # 检查列映射
            column_mapping = {
                '充电时间': data_config['timestamp_column'],
                '总有功功率_总和(kW)': data_config['target_column']
            }

            missing_columns = []
            for col in required_columns:
                # 检查原始列名或映射后的列名
                original_names = [k for k, v in column_mapping.items() if v == col]
                if col not in df.columns and not any(name in df.columns for name in original_names):
                    missing_columns.append(col)
                    # 检查英文列名
                    english_mapping = {
                        '充电时间': 'Timestamp',
                        '总有功功率_总和(kW)': 'Charging_Load_kW'
                    }
                    if english_mapping[col] not in df.columns:
                        missing_columns.append(col)
            
            if missing_columns:
                return {
                    'valid': False,
                    'message': f"缺少必要的列: {', '.join(missing_columns)}"
                }
            
            # 检查数据量
            if len(df) < 100:
                return {
                    'valid': False,
                    'message': "数据量太少，至少需要100行数据"
                }
            
            # 检查数据类型
            timestamp_col = 'Timestamp' if 'Timestamp' in df.columns else '充电时间'
            load_col = 'Charging_Load_kW' if 'Charging_Load_kW' in df.columns else '总有功功率_总和(kW)'
            
            try:
                pd.to_datetime(df[timestamp_col])
            except:
                return {
                    'valid': False,
                    'message': "时间列格式不正确"
                }
            
            try:
                pd.to_numeric(df[load_col])
            except:
                return {
                    'valid': False,
                    'message': "充电负荷列包含非数值数据"
                }
            
            return {
                'valid': True,
                'message': "数据验证通过",
                'info': {
                    'rows': len(df),
                    'columns': len(df.columns),
                    'time_range': f"{df[timestamp_col].min()} 到 {df[timestamp_col].max()}"
                }
            }
            
        except Exception as e:
            return {
                'valid': False,
                'message': f"数据验证失败: {str(e)}"
            }
            
    def train_model(
        self,
        config_updates: Optional[Dict[str, Any]] = None,
        progress_callback: Optional[Callable] = None,
        log_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """训练模型"""
        try:
            self.stop_flag = False

            # 更新配置
            if config_updates:
                self._update_configs(config_updates)

            # 设置日志回调
            if log_callback:
                self.log_capture.add_callback(log_callback)
                self.log_capture.start_capture()

            self.logger.info(f"开始训练，使用设备: {self.device}")

            # 检查数据
            if self.current_data is None:
                raise Exception("请先加载数据")

            # 初始化预测器
            training_config = self.config_manager.get_training_config()
            self.predictor = EVChargingPredictor(
                sequence_length=training_config['sequence_length'],
                device=self.device
            )

            if progress_callback:
                progress_callback('overall', 10)
                
            if log_callback:
                log_callback("准备数据...")
                
            # 保存数据到临时文件
            temp_file = "temp_training_data.csv"
            data.to_csv(temp_file, index=False, encoding='utf-8-sig')
            
            try:
                # 准备数据
                train_loader, val_loader, test_loader, target_scaler = self.predictor.prepare_data(temp_file)
                
                if progress_callback:
                    progress_callback('overall', 30)
                    
                if log_callback:
                    log_callback("数据准备完成，开始训练...")
                
                # 检查是否启用优化
                if config.get('enable_optimization', True):
                    if log_callback:
                        log_callback("开始超参数优化...")
                        
                    # 执行超参数优化
                    best_params = self.predictor.optimize_hyperparameters_advanced(
                        train_loader,
                        val_loader,
                        n_calls=config.get('opt_calls', 100),
                        enable_multi_objective=True,
                        enable_uncertainty_quantification=True
                    )
                    
                    if progress_callback:
                        progress_callback('overall', 60)
                        
                    if log_callback:
                        log_callback(f"超参数优化完成: {best_params}")
                else:
                    # 使用用户设置的参数
                    best_params = {
                        'hidden_size': config.get('hidden_size', 128),
                        'num_layers': config.get('num_layers', 3),
                        'kernel_size': config.get('kernel_size', 3),
                        'dropout': config.get('dropout', 0.3),
                        'learning_rate': config.get('learning_rate', 0.001),
                        'loss_type': 'MSELoss',
                        'optimizer_type': 'Adam',
                        'weight_decay': 1e-5
                    }
                    
                if self.stop_flag:
                    raise Exception("训练被用户停止")
                    
                # 执行交叉验证
                if log_callback:
                    log_callback("开始交叉验证...")
                    
                avg_results, fold_results, best_model_info = self.predictor.cross_validate(
                    temp_file,
                    n_splits=config.get('cv_folds', 5),
                    params=best_params
                )
                
                if progress_callback:
                    progress_callback('overall', 90)
                    
                if log_callback:
                    log_callback("交叉验证完成，评估最佳模型...")
                
                # 加载最佳模型进行最终评估
                model_config = {
                    'input_size': self.predictor.input_size,
                    'hidden_size': best_params['hidden_size'],
                    'num_layers': best_params['num_layers'],
                    'output_size': 1,
                    'kernel_size': best_params['kernel_size'],
                    'dropout': best_params['dropout'],
                    'learning_rate': best_params['learning_rate'],
                    'loss_type': best_params['loss_type'],
                    'optimizer_type': best_params['optimizer_type'],
                    'weight_decay': best_params['weight_decay']
                }
                
                best_model = create_model(model_config).to(self.predictor.device)
                best_model.load_state_dict(torch.load(best_model_info['model_path']))
                
                # 在测试集上评估
                mse, rmse, mae, mape, accuracy, r2, predictions_orig, actuals_orig = self.predictor.evaluate(
                    best_model, test_loader, target_scaler
                )
                
                if progress_callback:
                    progress_callback('overall', 100)
                    
                if log_callback:
                    log_callback("训练完成！")
                
                # 返回结果
                return {
                    'model': best_model,
                    'model_path': best_model_info['model_path'],
                    'config': model_config,
                    'best_params': best_params,
                    'metrics': {
                        'mse': mse,
                        'rmse': rmse,
                        'mae': mae,
                        'mape': mape,
                        'accuracy': accuracy,
                        'r2': r2
                    },
                    'predictions': predictions_orig.flatten(),
                    'actuals': actuals_orig.flatten(),
                    'cross_validation_results': avg_results,
                    'fold_results': fold_results,
                    'target_scaler': target_scaler
                }
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    
        except Exception as e:
            if log_callback:
                log_callback(f"训练失败: {str(e)}")
            raise Exception(f"模型训练失败: {str(e)}")
            
    def stop_training(self):
        """停止训练"""
        self.stop_flag = True
        if self.predictor:
            # 这里可以添加停止训练的逻辑
            pass
            
    def predict(self, model, data_file: str) -> list:
        """使用模型进行预测
        
        Args:
            model: 训练好的模型
            data_file: 预测数据文件
            
        Returns:
            预测结果列表
        """
        try:
            # 加载预测数据
            df = self.load_data(data_file)
            
            # 这里需要实现预测逻辑
            # 简化版本，返回模拟结果
            predictions = []
            for i in range(min(100, len(df))):
                predictions.append({
                    'timestamp': df.iloc[i]['Timestamp'] if 'Timestamp' in df.columns else f'样本 {i+1}',
                    'value': np.random.uniform(10, 100),  # 模拟预测值
                    'confidence': np.random.uniform(1, 5)  # 模拟置信区间
                })
            
            return predictions
            
        except Exception as e:
            raise Exception(f"预测失败: {str(e)}")
            
    def load_model(self, model_path: str):
        """加载模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            加载的模型
        """
        try:
            # 这里需要实现模型加载逻辑
            # 简化版本
            if not os.path.exists(model_path):
                raise Exception("模型文件不存在")
            
            # 返回模拟模型对象
            return {"model_path": model_path, "loaded": True}
            
        except Exception as e:
            raise Exception(f"模型加载失败: {str(e)}")
            
    def save_model(self, model, file_path: str):
        """保存模型
        
        Args:
            model: 要保存的模型
            file_path: 保存路径
        """
        try:
            torch.save(model.state_dict(), file_path)
        except Exception as e:
            raise Exception(f"模型保存失败: {str(e)}")
            
    def export_report(self, results: Dict[str, Any], file_path: str):
        """导出结果报告
        
        Args:
            results: 训练结果
            file_path: 导出文件路径
        """
        try:
            # 生成HTML报告
            html_content = self._generate_html_report(results)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
        except Exception as e:
            raise Exception(f"报告导出失败: {str(e)}")
            
    def export_predictions(self, results: Dict[str, Any], file_path: str):
        """导出预测数据
        
        Args:
            results: 训练结果
            file_path: 导出文件路径
        """
        try:
            if 'predictions' in results and 'actuals' in results:
                df = pd.DataFrame({
                    '预测值': results['predictions'],
                    '实际值': results['actuals'],
                    '误差': results['predictions'] - results['actuals']
                })
                
                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                    
        except Exception as e:
            raise Exception(f"预测数据导出失败: {str(e)}")
            
    def get_gpu_info(self) -> str:
        """获取GPU信息"""
        try:
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                return f"{gpu_name} ({gpu_memory:.1f}GB)"
            else:
                return "不可用"
        except:
            return "检测失败"
            
    def get_memory_info(self) -> str:
        """获取内存信息"""
        try:
            memory = psutil.virtual_memory()
            used_gb = memory.used / 1024**3
            total_gb = memory.total / 1024**3
            return f"{used_gb:.1f}GB / {total_gb:.1f}GB ({memory.percent:.1f}%)"
        except:
            return "检测失败"
            
    def _generate_html_report(self, results: Dict[str, Any]) -> str:
        """生成HTML报告"""
        metrics = results.get('metrics', {})
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>电动汽车充电负荷预测结果报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #333; }}
                .metrics {{ display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }}
                .metric-card {{ border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
                .metric-label {{ color: #666; margin-top: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>电动汽车充电负荷预测结果报告</h1>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value">{metrics.get('mse', 0):.4f}</div>
                    <div class="metric-label">均方误差 (MSE)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{metrics.get('rmse', 0):.4f}</div>
                    <div class="metric-label">均方根误差 (RMSE)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{metrics.get('mae', 0):.4f}</div>
                    <div class="metric-label">平均绝对误差 (MAE)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{metrics.get('mape', 0):.2f}%</div>
                    <div class="metric-label">平均绝对百分比误差 (MAPE)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{metrics.get('accuracy', 0):.2f}%</div>
                    <div class="metric-label">预测准确率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{metrics.get('r2', 0):.4f}</div>
                    <div class="metric-label">决定系数 (R²)</div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
