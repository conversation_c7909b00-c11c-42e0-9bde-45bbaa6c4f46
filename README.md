# 电动汽车充电负荷预测系统

## 项目概述

本项目是一个基于深度学习的电动汽车充电负荷时间序列预测系统，采用先进的IBOTCNGRU模型（集成时间卷积网络、门控循环单元和注意力机制），结合贝叶斯优化进行超参数调优，提供高精度的充电负荷预测。

### 核心特性

- **先进的模型架构**: IBOTCNGRU模型，融合TCN、GRU和注意力机制
- **智能超参数优化**: 贝叶斯优化自动调参
- **图形化界面**: 直观易用的GUI界面，无需编程知识
- **实时可视化**: 训练过程和预测结果的实时图表展示
- **多种评估指标**: MSE、RMSE、MAE、MAPE、R²等全面评估
- **交叉验证**: 时间序列交叉验证确保模型稳定性

## 系统架构

### 核心模块

```
├── config.py              # 统一配置管理系统
├── logger.py              # 统一日志系统
├── model.py               # IBOTCNGRU模型定义
├── train.py               # 核心训练模块
├── data_preprocessing.py  # 数据预处理模块
├── advanced_bayesian_optimizer.py  # 贝叶斯优化器
├── optimization_visualizer.py      # 优化过程可视化
└── gui/                   # 图形界面模块
    ├── gui_main.py        # 主界面
    ├── gui_backend.py     # 后端服务
    └── gui_components.py  # 界面组件
```

### 技术栈

- **深度学习框架**: PyTorch
- **数据处理**: Pandas, NumPy
- **可视化**: Matplotlib, Seaborn
- **GUI框架**: Tkinter
- **优化算法**: Scikit-optimize (贝叶斯优化)
- **配置管理**: JSON + Dataclass

## 快速开始

### 环境要求

- Python 3.8+
- PyTorch 1.8+
- CUDA (可选，用于GPU加速)

### 安装依赖

```bash
pip install torch pandas numpy matplotlib seaborn scikit-optimize scikit-learn
```

### 启动GUI界面

```bash
python run_gui.py
```

### 使用命令行

```bash
python train.py --data_file your_data.csv --epochs 400
```

## 使用指南

### 1. 数据准备

数据文件应为CSV格式，包含以下列：

**中文列名**:
- `充电时间` 或 `Timestamp`: 时间戳
- `总有功功率_总和(kW)` 或 `Charging_Load_kW`: 充电负荷
- `降水量(mm)` 或 `Precipitation_mm`: 降水量（可选）
- `平均气温(℃)` 或 `Average_Temperature_C`: 平均气温（可选）

**示例数据格式**:
```csv
充电时间,总有功功率_总和(kW),降水量(mm),平均气温(℃)
2023-01-01 00:00:00,150.5,0.0,5.2
2023-01-01 01:00:00,120.3,0.0,4.8
...
```

### 2. GUI操作流程

#### 步骤1: 数据导入
1. 点击"数据导入"标签页
2. 选择数据文件
3. 查看数据预览和统计信息
4. 确认数据格式正确

#### 步骤2: 参数设置
1. 切换到"参数设置"标签页
2. 调整模型参数：
   - 序列长度：预测窗口大小（默认24小时）
   - 隐藏层大小：神经网络复杂度（默认128）
   - 学习率：训练速度（默认0.001）
   - 训练轮数：迭代次数（默认400）
3. 选择优化选项：
   - 启用贝叶斯优化：自动调参
   - 多目标优化：平衡精度和复杂度
   - 不确定性量化：提供预测置信区间

#### 步骤3: 模型训练
1. 进入"训练控制"标签页
2. 点击"开始训练"按钮
3. 实时监控训练进度和日志
4. 查看损失函数变化曲线
5. 等待训练完成

#### 步骤4: 结果分析
1. 切换到"结果可视化"标签页
2. 查看预测结果对比图
3. 分析各项评估指标
4. 导出预测结果和模型

#### 步骤5: 预测服务
1. 进入"预测服务"标签页
2. 加载训练好的模型
3. 输入新数据进行预测
4. 获取预测结果和置信区间

### 3. 配置文件

系统使用统一的配置管理，配置文件为`config.json`：

```json
{
    "model": {
        "input_size": 1,
        "hidden_size": 128,
        "num_layers": 3,
        "kernel_size": 3,
        "dropout": 0.3
    },
    "training": {
        "sequence_length": 24,
        "batch_size": 64,
        "learning_rate": 0.001,
        "epochs": 400,
        "device": "auto"
    },
    "optimization": {
        "enable_bayesian_optimization": false,
        "n_calls": 100,
        "enable_multi_objective": true
    }
}
```

## 模型说明

### IBOTCNGRU架构

1. **时间卷积网络(TCN)**: 捕获长期时间依赖关系
2. **门控循环单元(GRU)**: 处理序列信息和短期依赖
3. **注意力机制**: 自适应关注重要时间步
4. **残差连接**: 缓解梯度消失问题
5. **Dropout正则化**: 防止过拟合

### 贝叶斯优化

- **高斯过程**: 建模超参数空间
- **采集函数**: 平衡探索与利用
- **多目标优化**: 同时优化精度和复杂度
- **不确定性量化**: 提供预测置信度

## 性能指标

系统提供多种评估指标：

- **MSE**: 均方误差
- **RMSE**: 均方根误差  
- **MAE**: 平均绝对误差
- **MAPE**: 平均绝对百分比误差
- **R²**: 决定系数

## 输出文件

训练完成后，系统会生成以下文件：

```
outputs/
├── models/                 # 保存的模型文件
├── results/               # 预测结果
├── logs/                  # 训练日志
└── visualizations/        # 可视化图表
```

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小batch_size
   - 使用CPU训练（设置device="cpu"）

2. **数据格式错误**
   - 检查CSV文件编码（建议UTF-8）
   - 确认列名格式正确
   - 检查时间格式

3. **训练收敛慢**
   - 调整学习率
   - 启用贝叶斯优化
   - 增加训练轮数

4. **预测精度低**
   - 增加序列长度
   - 调整模型复杂度
   - 检查数据质量

### 日志分析

系统提供详细的日志信息：
- 训练进度和损失变化
- 模型性能指标
- 错误和警告信息
- 系统资源使用情况

## 技术支持

如遇到问题，请检查：
1. 系统日志文件
2. 数据格式是否正确
3. 依赖包版本兼容性
4. 硬件资源是否充足

## 更新日志

### v2.0 (当前版本)
- 重构项目架构，简化代码结构
- 统一配置管理系统
- 改进日志系统
- 优化GUI界面
- 删除冗余文件和测试脚本

### v1.0
- 初始版本发布
- 基础IBOTCNGRU模型
- GUI界面实现
- 贝叶斯优化集成
