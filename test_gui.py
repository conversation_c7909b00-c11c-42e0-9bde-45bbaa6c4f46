#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 简化版本用于测试基本功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class SimpleGUITest:
    """简化的GUI测试类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("电动汽车充电负荷预测系统 - 测试版")
        self.root.geometry("800x600")
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = ttk.Label(
            self.root, 
            text="电动汽车充电负荷预测系统", 
            font=('Microsoft YaHei', 16, 'bold')
        )
        title_label.pack(pady=10)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 数据导入选项卡
        self.create_data_tab()
        
        # 参数设置选项卡
        self.create_param_tab()
        
        # 测试选项卡
        self.create_test_tab()
        
        # 状态栏
        self.status_var = tk.StringVar(value="系统就绪")
        status_label = ttk.Label(self.root, textvariable=self.status_var)
        status_label.pack(side='bottom', fill='x')
        
    def create_data_tab(self):
        """创建数据导入选项卡"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="数据导入")
        
        # 文件选择
        file_frame = ttk.LabelFrame(data_frame, text="文件选择", padding=10)
        file_frame.pack(fill='x', padx=10, pady=5)
        
        self.file_var = tk.StringVar()
        ttk.Label(file_frame, text="数据文件:").pack(anchor='w')
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill='x', pady=5)
        
        ttk.Entry(file_select_frame, textvariable=self.file_var, width=60).pack(side='left', fill='x', expand=True)
        ttk.Button(file_select_frame, text="浏览", command=self.browse_file).pack(side='right', padx=(5, 0))
        
        # 操作按钮
        btn_frame = ttk.Frame(data_frame)
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(btn_frame, text="加载数据", command=self.load_data).pack(side='left')
        ttk.Button(btn_frame, text="预览数据", command=self.preview_data).pack(side='left', padx=(10, 0))
        
        # 数据预览区域
        preview_frame = ttk.LabelFrame(data_frame, text="数据预览", padding=10)
        preview_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.preview_text = tk.Text(preview_frame, height=15)
        scrollbar = ttk.Scrollbar(preview_frame, orient='vertical', command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=scrollbar.set)
        
        self.preview_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
    def create_param_tab(self):
        """创建参数设置选项卡"""
        param_frame = ttk.Frame(self.notebook)
        self.notebook.add(param_frame, text="参数设置")
        
        # 基础参数
        basic_frame = ttk.LabelFrame(param_frame, text="基础参数", padding=10)
        basic_frame.pack(fill='x', padx=10, pady=5)
        
        # 序列长度
        ttk.Label(basic_frame, text="序列长度:").grid(row=0, column=0, sticky='w', pady=2)
        self.sequence_length_var = tk.IntVar(value=24)
        ttk.Spinbox(basic_frame, from_=12, to=168, textvariable=self.sequence_length_var, width=10).grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 设备选择
        ttk.Label(basic_frame, text="计算设备:").grid(row=1, column=0, sticky='w', pady=2)
        self.device_var = tk.StringVar(value="auto")
        ttk.Combobox(basic_frame, textvariable=self.device_var, values=["auto", "cpu", "cuda"], width=10).grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(param_frame)
        btn_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(btn_frame, text="重置默认值", command=self.reset_params).pack(side='left')
        ttk.Button(btn_frame, text="保存配置", command=self.save_config).pack(side='left', padx=(10, 0))
        
    def create_test_tab(self):
        """创建测试选项卡"""
        test_frame = ttk.Frame(self.notebook)
        self.notebook.add(test_frame, text="功能测试")
        
        # 测试按钮
        btn_frame = ttk.LabelFrame(test_frame, text="功能测试", padding=10)
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(btn_frame, text="测试matplotlib", command=self.test_matplotlib).pack(side='left')
        ttk.Button(btn_frame, text="测试数据处理", command=self.test_data_processing).pack(side='left', padx=(10, 0))
        ttk.Button(btn_frame, text="测试PyTorch", command=self.test_pytorch).pack(side='left', padx=(10, 0))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(test_frame, text="测试结果", padding=10)
        result_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.result_text = tk.Text(result_frame, height=20)
        result_scrollbar = ttk.Scrollbar(result_frame, orient='vertical', command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side='left', fill='both', expand=True)
        result_scrollbar.pack(side='right', fill='y')
        
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
            self.status_var.set(f"已选择文件: {filename}")
            
    def load_data(self):
        """加载数据"""
        filename = self.file_var.get()
        if not filename:
            messagebox.showwarning("警告", "请先选择数据文件")
            return
            
        try:
            if filename.endswith('.csv'):
                df = pd.read_csv(filename, encoding='utf-8-sig')
            else:
                df = pd.read_excel(filename)
                
            self.current_data = df
            self.status_var.set(f"数据加载成功: {df.shape[0]} 行 × {df.shape[1]} 列")
            messagebox.showinfo("成功", "数据加载成功！")
            
        except Exception as e:
            messagebox.showerror("错误", f"数据加载失败: {str(e)}")
            
    def preview_data(self):
        """预览数据"""
        if not hasattr(self, 'current_data'):
            messagebox.showwarning("警告", "请先加载数据")
            return
            
        try:
            # 显示数据信息
            info = f"数据形状: {self.current_data.shape}\n\n"
            info += f"列名:\n{list(self.current_data.columns)}\n\n"
            info += f"前5行数据:\n{self.current_data.head().to_string()}\n\n"
            info += f"数据类型:\n{self.current_data.dtypes.to_string()}\n\n"
            info += f"统计信息:\n{self.current_data.describe().to_string()}"
            
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, info)
            
        except Exception as e:
            messagebox.showerror("错误", f"数据预览失败: {str(e)}")
            
    def reset_params(self):
        """重置参数"""
        self.sequence_length_var.set(24)
        self.device_var.set("auto")
        self.status_var.set("参数已重置为默认值")
        
    def save_config(self):
        """保存配置"""
        config = {
            'sequence_length': self.sequence_length_var.get(),
            'device': self.device_var.get()
        }
        
        filename = filedialog.asksaveasfilename(
            title="保存配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json")]
        )
        
        if filename:
            try:
                import json
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)
                messagebox.showinfo("成功", "配置保存成功！")
            except Exception as e:
                messagebox.showerror("错误", f"配置保存失败: {str(e)}")
                
    def test_matplotlib(self):
        """测试matplotlib"""
        try:
            # 创建测试图表
            fig, ax = plt.subplots(figsize=(8, 4))
            x = np.linspace(0, 10, 100)
            y = np.sin(x)
            ax.plot(x, y, label='sin(x)')
            ax.set_title('Matplotlib测试图表')
            ax.set_xlabel('x')
            ax.set_ylabel('y')
            ax.legend()
            ax.grid(True)
            
            # 显示图表
            plt.show()
            
            self.add_test_result("matplotlib测试", "成功", "图表显示正常")
            
        except Exception as e:
            self.add_test_result("matplotlib测试", "失败", str(e))
            
    def test_data_processing(self):
        """测试数据处理"""
        try:
            # 创建测试数据
            data = pd.DataFrame({
                'time': pd.date_range('2023-01-01', periods=100, freq='H'),
                'value': np.random.randn(100) * 10 + 50
            })
            
            # 基本统计
            stats = data['value'].describe()
            
            self.add_test_result("数据处理测试", "成功", f"创建了{len(data)}行测试数据\n统计信息:\n{stats}")
            
        except Exception as e:
            self.add_test_result("数据处理测试", "失败", str(e))
            
    def test_pytorch(self):
        """测试PyTorch"""
        try:
            import torch
            
            # 检查CUDA
            cuda_available = torch.cuda.is_available()
            device_count = torch.cuda.device_count() if cuda_available else 0
            
            # 创建测试张量
            x = torch.randn(10, 5)
            y = torch.randn(10, 1)
            
            # 简单线性层
            linear = torch.nn.Linear(5, 1)
            output = linear(x)
            
            result = f"PyTorch版本: {torch.__version__}\n"
            result += f"CUDA可用: {cuda_available}\n"
            result += f"GPU数量: {device_count}\n"
            result += f"测试张量形状: {x.shape} -> {output.shape}"
            
            self.add_test_result("PyTorch测试", "成功", result)
            
        except Exception as e:
            self.add_test_result("PyTorch测试", "失败", str(e))
            
    def add_test_result(self, test_name, status, details):
        """添加测试结果"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        result = f"[{timestamp}] {test_name}: {status}\n"
        result += f"详情: {details}\n"
        result += "-" * 50 + "\n"
        
        self.result_text.insert(tk.END, result)
        self.result_text.see(tk.END)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动GUI测试...")
    
    try:
        app = SimpleGUITest()
        print("GUI测试界面创建成功")
        app.run()
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
