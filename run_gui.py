import sys
import os
import tkinter as tk
from tkinter import messagebox
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

def check_dependencies():
    """检查核心依赖"""
    required_packages = [
        'torch',
        'pandas',
        'numpy',
        'matplotlib',
        'sklearn'
    ]

    missing_packages = []
    print("🔍 检查依赖包...")

    for package in required_packages:
        try:
            print(f"  检查 {package}...", end=" ")
            __import__(package)
            print("✓")
        except ImportError:
            print("❌")
            missing_packages.append(package)

    if missing_packages:
        error_msg = f"缺少依赖包: {', '.join(missing_packages)}\n\n安装命令:\npip install {' '.join(missing_packages)}"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("依赖检查失败", error_msg)
        except:
            pass
        return False

    print("✅ 依赖检查通过")
    return True

def check_files():
    """检查项目文件"""
    required_files = [
        'config.py',
        'logger.py',
        'gui_main.py',
        'gui_backend.py',
        'gui_components.py',
        'train.py',
        'model.py',
        'data_preprocessing.py'
    ]

    missing_files = []
    print("🔍 检查项目文件...")
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        error_msg = f"缺少以下必要文件:\n{', '.join(missing_files)}\n\n请确保所有文件都在当前目录中。"
        messagebox.showerror("文件检查失败", error_msg)
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("电动汽车充电负荷预测系统 - GUI版本")
    print("=" * 60)

    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        try:
            messagebox.showerror("版本错误", "需要Python 3.7或更高版本")
        except:
            pass
        return

    print("✓ Python版本检查通过")

    # 检查依赖项
    print("正在检查依赖项...")
    if not check_dependencies():
        return
    print("✓ 依赖项检查通过")

    # 检查文件
    print("正在检查必要文件...")
    if not check_files():
        return
    print("✓ 文件检查通过")

    try:
        print("正在导入GUI模块...")

        # 先测试基本导入
        import tkinter as tk
        print("  - tkinter导入成功")

        import matplotlib.pyplot as plt
        print("  - matplotlib导入成功")

        import pandas as pd
        print("  - pandas导入成功")

        import torch
        print("  - torch导入成功")

        # 导入GUI主模块
        from gui_main import EVChargingPredictionGUI
        print("  - GUI主模块导入成功")

        print("正在启动GUI界面...")

        # 创建并运行GUI应用
        app = EVChargingPredictionGUI()

        print("✓ GUI界面启动成功")
        print("\n使用说明:")
        print("1. 在'数据导入'选项卡中选择并加载训练数据")
        print("2. 在'参数设置'选项卡中配置模型参数")
        print("3. 在'模型训练'选项卡中开始训练")
        print("4. 在'结果查看'选项卡中查看训练结果")
        print("5. 在'预测服务'选项卡中使用训练好的模型进行预测")
        print("\n提示: 可以使用生成的演示数据文件进行测试")
        print("- small_demo_data.csv: 小规模测试数据")
        print("- demo_charging_data.csv: 完整训练数据")
        print("\n" + "=" * 60)

        # 运行GUI
        app.run()

    except ImportError as e:
        error_msg = f"导入模块失败: {str(e)}\n\n请确保所有必要的文件都在当前目录中。"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("导入错误", error_msg)
        except:
            pass

    except Exception as e:
        error_msg = f"启动GUI失败: {str(e)}"
        print(f"❌ {error_msg}")
        import traceback
        traceback.print_exc()
        try:
            messagebox.showerror("启动错误", error_msg)
        except:
            pass

    print("\n程序已退出")

if __name__ == "__main__":
    main()
