#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一日志系统
"""

import logging
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Union
import threading

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class ThreadSafeLogger:
    """线程安全的日志管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self.loggers = {}
            self.setup_default_logger()
    
    def setup_default_logger(self):
        """设置默认日志器"""
        self.setup_logger('main', level='INFO')
    
    def setup_logger(
        self,
        name: str,
        level: Union[str, int] = 'INFO',
        log_file: Optional[str] = None,
        console_output: bool = True,
        file_output: bool = True
    ) -> logging.Logger:
        """
        设置日志器
        
        Args:
            name: 日志器名称
            level: 日志级别
            log_file: 日志文件路径
            console_output: 是否输出到控制台
            file_output: 是否输出到文件
        
        Returns:
            配置好的日志器
        """
        
        # 如果日志器已存在，直接返回
        if name in self.loggers:
            return self.loggers[name]
        
        # 创建日志器
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()) if isinstance(level, str) else level)
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式化器
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if file_output:
            if log_file is None:
                # 创建logs目录
                log_dir = Path('logs')
                log_dir.mkdir(exist_ok=True)
                
                # 生成日志文件名
                timestamp = datetime.now().strftime('%Y%m%d')
                log_file = log_dir / f'{name}_{timestamp}.log'
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        # 防止日志传播到父日志器
        logger.propagate = False
        
        # 保存日志器
        self.loggers[name] = logger
        
        return logger
    
    def get_logger(self, name: str = 'main') -> logging.Logger:
        """获取日志器"""
        if name not in self.loggers:
            return self.setup_logger(name)
        return self.loggers[name]
    
    def set_level(self, name: str, level: Union[str, int]):
        """设置日志级别"""
        if name in self.loggers:
            self.loggers[name].setLevel(
                getattr(logging, level.upper()) if isinstance(level, str) else level
            )

class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        class_name = self.__class__.__name__
        return get_logger(class_name)

# 全局日志管理器实例
_logger_manager = ThreadSafeLogger()

def setup_logger(
    name: str,
    level: Union[str, int] = 'INFO',
    log_file: Optional[str] = None,
    console_output: bool = True,
    file_output: bool = True
) -> logging.Logger:
    """设置日志器的便捷函数"""
    return _logger_manager.setup_logger(name, level, log_file, console_output, file_output)

def get_logger(name: str = 'main') -> logging.Logger:
    """获取日志器的便捷函数"""
    return _logger_manager.get_logger(name)

def set_log_level(name: str, level: Union[str, int]):
    """设置日志级别的便捷函数"""
    _logger_manager.set_level(name, level)

# 设置默认的根日志器
def setup_root_logger(level: str = 'INFO'):
    """设置根日志器"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

class ProgressLogger:
    """进度日志器"""
    
    def __init__(self, logger: logging.Logger, total: int, desc: str = "Progress"):
        self.logger = logger
        self.total = total
        self.current = 0
        self.desc = desc
        self.last_percent = -1
    
    def update(self, n: int = 1):
        """更新进度"""
        self.current += n
        percent = int((self.current / self.total) * 100)
        
        # 只在百分比变化时记录日志
        if percent != self.last_percent and percent % 10 == 0:
            self.logger.info(f"{self.desc}: {percent}% ({self.current}/{self.total})")
            self.last_percent = percent
    
    def finish(self):
        """完成进度"""
        self.logger.info(f"{self.desc}: 100% ({self.total}/{self.total}) - 完成")

class LogCapture:
    """日志捕获器，用于GUI显示"""
    
    def __init__(self, logger_name: str = 'main'):
        self.logger_name = logger_name
        self.logs = []
        self.handler = None
        self.callbacks = []
    
    def start_capture(self):
        """开始捕获日志"""
        logger = get_logger(self.logger_name)
        
        # 创建内存处理器
        self.handler = MemoryHandler(self.logs, self.callbacks)
        logger.addHandler(self.handler)
    
    def stop_capture(self):
        """停止捕获日志"""
        if self.handler:
            logger = get_logger(self.logger_name)
            logger.removeHandler(self.handler)
            self.handler = None
    
    def get_logs(self) -> list:
        """获取捕获的日志"""
        return self.logs.copy()
    
    def clear_logs(self):
        """清除日志"""
        self.logs.clear()
    
    def add_callback(self, callback):
        """添加日志回调函数"""
        self.callbacks.append(callback)

class MemoryHandler(logging.Handler):
    """内存日志处理器"""
    
    def __init__(self, logs_list: list, callbacks: list):
        super().__init__()
        self.logs_list = logs_list
        self.callbacks = callbacks
        
        # 设置格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.setFormatter(formatter)
    
    def emit(self, record):
        """发出日志记录"""
        try:
            msg = self.format(record)
            self.logs_list.append(msg)
            
            # 限制日志数量
            if len(self.logs_list) > 1000:
                self.logs_list.pop(0)
            
            # 调用回调函数
            for callback in self.callbacks:
                try:
                    callback(msg)
                except Exception:
                    pass  # 忽略回调函数中的错误
                    
        except Exception:
            self.handleError(record)

# 初始化默认日志系统
setup_root_logger()

# 导出主要接口
__all__ = [
    'setup_logger',
    'get_logger', 
    'set_log_level',
    'LoggerMixin',
    'ProgressLogger',
    'LogCapture',
    'setup_root_logger'
]
