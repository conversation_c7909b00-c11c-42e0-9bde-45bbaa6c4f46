#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电动汽车充电负荷预测系统 - 主界面
提供直观的图形化操作界面，将复杂的预测模型转化为一键式操作
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import numpy as np

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

try:
    from gui_backend import PredictionBackend
    from gui_components import *
except ImportError as e:
    print(f"导入GUI模块失败: {e}")
    import sys
    sys.exit(1)

class EVChargingPredictionGUI:
    """电动汽车充电负荷预测系统主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("电动汽车充电负荷预测系统 v1.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化后端服务
        self.backend = PredictionBackend()
        self.message_queue = queue.Queue()
        
        # 状态变量
        self.current_data = None
        self.current_model = None
        self.training_thread = None
        self.is_training = False
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        
        # 启动消息处理
        self.process_messages()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Microsoft YaHei', 12, 'bold'))
        style.configure('Status.TLabel', font=('Microsoft YaHei', 9))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        self.title_frame = ttk.Frame(self.root)
        self.title_label = ttk.Label(
            self.title_frame, 
            text="电动汽车充电负荷预测系统", 
            style='Title.TLabel'
        )
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.root)
        
        # 数据导入选项卡
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="数据导入")
        self.create_data_import_widgets()
        
        # 参数设置选项卡
        self.param_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.param_frame, text="参数设置")
        self.create_parameter_widgets()
        
        # 模型训练选项卡
        self.train_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.train_frame, text="模型训练")
        self.create_training_widgets()
        
        # 结果查看选项卡
        self.result_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.result_frame, text="结果查看")
        self.create_result_widgets()
        
        # 预测服务选项卡
        self.predict_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.predict_frame, text="预测服务")
        self.create_prediction_widgets()
        
        # 状态栏
        self.status_frame = ttk.Frame(self.root)
        self.create_status_bar()
        
    def create_data_import_widgets(self):
        """创建数据导入界面"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.data_frame, text="数据文件选择", padding=10)
        file_frame.pack(fill='x', padx=10, pady=5)
        
        self.file_path_var = tk.StringVar()
        ttk.Label(file_frame, text="数据文件:").pack(anchor='w')
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill='x', pady=5)
        
        self.file_entry = ttk.Entry(file_select_frame, textvariable=self.file_path_var, width=60)
        self.file_entry.pack(side='left', fill='x', expand=True)
        
        self.browse_btn = ttk.Button(
            file_select_frame, 
            text="浏览", 
            command=self.browse_file,
            width=10
        )
        self.browse_btn.pack(side='right', padx=(5, 0))
        
        # 数据预览区域
        preview_frame = ttk.LabelFrame(self.data_frame, text="数据预览", padding=10)
        preview_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建表格
        self.data_tree = ttk.Treeview(preview_frame, height=15)
        self.data_tree.pack(side='left', fill='both', expand=True)
        
        # 滚动条
        data_scrollbar = ttk.Scrollbar(preview_frame, orient='vertical', command=self.data_tree.yview)
        data_scrollbar.pack(side='right', fill='y')
        self.data_tree.configure(yscrollcommand=data_scrollbar.set)
        
        # 操作按钮
        btn_frame = ttk.Frame(self.data_frame)
        btn_frame.pack(fill='x', padx=10, pady=5)
        
        self.load_btn = ttk.Button(
            btn_frame, 
            text="加载数据", 
            command=self.load_data,
            state='disabled'
        )
        self.load_btn.pack(side='left')
        
        self.validate_btn = ttk.Button(
            btn_frame, 
            text="验证数据", 
            command=self.validate_data,
            state='disabled'
        )
        self.validate_btn.pack(side='left', padx=(10, 0))
        
        # 数据信息显示
        self.data_info_var = tk.StringVar(value="请选择数据文件")
        self.data_info_label = ttk.Label(btn_frame, textvariable=self.data_info_var, style='Status.TLabel')
        self.data_info_label.pack(side='right')
        
    def create_parameter_widgets(self):
        """创建参数设置界面"""
        # 创建滚动区域
        canvas = tk.Canvas(self.param_frame)
        scrollbar = ttk.Scrollbar(self.param_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基础参数
        basic_frame = ttk.LabelFrame(scrollable_frame, text="基础参数", padding=10)
        basic_frame.pack(fill='x', padx=10, pady=5)
        
        # 序列长度
        ttk.Label(basic_frame, text="序列长度:").grid(row=0, column=0, sticky='w', pady=2)
        self.sequence_length_var = tk.IntVar(value=24)
        sequence_spin = ttk.Spinbox(basic_frame, from_=12, to=168, textvariable=self.sequence_length_var, width=10)
        sequence_spin.grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)
        ttk.Label(basic_frame, text="小时").grid(row=0, column=2, sticky='w', padx=(5, 0), pady=2)
        
        # 设备选择
        ttk.Label(basic_frame, text="计算设备:").grid(row=1, column=0, sticky='w', pady=2)
        self.device_var = tk.StringVar(value="auto")
        device_combo = ttk.Combobox(basic_frame, textvariable=self.device_var, values=["auto", "cpu", "cuda"], width=10)
        device_combo.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 模型参数
        model_frame = ttk.LabelFrame(scrollable_frame, text="模型参数", padding=10)
        model_frame.pack(fill='x', padx=10, pady=5)
        
        # 隐藏层大小
        ttk.Label(model_frame, text="隐藏层大小:").grid(row=0, column=0, sticky='w', pady=2)
        self.hidden_size_var = tk.IntVar(value=128)
        hidden_spin = ttk.Spinbox(model_frame, from_=64, to=512, increment=64, textvariable=self.hidden_size_var, width=10)
        hidden_spin.grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 网络层数
        ttk.Label(model_frame, text="网络层数:").grid(row=1, column=0, sticky='w', pady=2)
        self.num_layers_var = tk.IntVar(value=3)
        layers_spin = ttk.Spinbox(model_frame, from_=1, to=8, textvariable=self.num_layers_var, width=10)
        layers_spin.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 卷积核大小
        ttk.Label(model_frame, text="卷积核大小:").grid(row=2, column=0, sticky='w', pady=2)
        self.kernel_size_var = tk.IntVar(value=3)
        kernel_spin = ttk.Spinbox(model_frame, from_=2, to=12, textvariable=self.kernel_size_var, width=10)
        kernel_spin.grid(row=2, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # Dropout率
        ttk.Label(model_frame, text="Dropout率:").grid(row=3, column=0, sticky='w', pady=2)
        self.dropout_var = tk.DoubleVar(value=0.3)
        dropout_spin = ttk.Spinbox(model_frame, from_=0.1, to=0.6, increment=0.1, textvariable=self.dropout_var, width=10)
        dropout_spin.grid(row=3, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 训练参数
        train_param_frame = ttk.LabelFrame(scrollable_frame, text="训练参数", padding=10)
        train_param_frame.pack(fill='x', padx=10, pady=5)
        
        # 学习率
        ttk.Label(train_param_frame, text="学习率:").grid(row=0, column=0, sticky='w', pady=2)
        self.learning_rate_var = tk.DoubleVar(value=0.001)
        lr_entry = ttk.Entry(train_param_frame, textvariable=self.learning_rate_var, width=12)
        lr_entry.grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 批次大小
        ttk.Label(train_param_frame, text="批次大小:").grid(row=1, column=0, sticky='w', pady=2)
        self.batch_size_var = tk.IntVar(value=64)
        batch_spin = ttk.Spinbox(train_param_frame, from_=16, to=256, increment=16, textvariable=self.batch_size_var, width=10)
        batch_spin.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 优化参数
        opt_frame = ttk.LabelFrame(scrollable_frame, text="优化参数", padding=10)
        opt_frame.pack(fill='x', padx=10, pady=5)
        
        # 是否启用贝叶斯优化
        self.enable_optimization_var = tk.BooleanVar(value=True)
        opt_check = ttk.Checkbutton(
            opt_frame, 
            text="启用贝叶斯超参数优化", 
            variable=self.enable_optimization_var,
            command=self.toggle_optimization_params
        )
        opt_check.grid(row=0, column=0, columnspan=3, sticky='w', pady=2)
        
        # 优化调用次数
        self.opt_calls_label = ttk.Label(opt_frame, text="优化调用次数:")
        self.opt_calls_label.grid(row=1, column=0, sticky='w', pady=2)
        self.opt_calls_var = tk.IntVar(value=100)
        self.opt_calls_spin = ttk.Spinbox(opt_frame, from_=50, to=300, increment=50, textvariable=self.opt_calls_var, width=10)
        self.opt_calls_spin.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 交叉验证折数
        ttk.Label(opt_frame, text="交叉验证折数:").grid(row=2, column=0, sticky='w', pady=2)
        self.cv_folds_var = tk.IntVar(value=5)
        cv_spin = ttk.Spinbox(opt_frame, from_=3, to=10, textvariable=self.cv_folds_var, width=10)
        cv_spin.grid(row=2, column=1, sticky='w', padx=(10, 0), pady=2)
        
        # 按钮区域
        param_btn_frame = ttk.Frame(scrollable_frame)
        param_btn_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(param_btn_frame, text="重置默认值", command=self.reset_parameters).pack(side='left')
        ttk.Button(param_btn_frame, text="保存配置", command=self.save_config).pack(side='left', padx=(10, 0))
        ttk.Button(param_btn_frame, text="加载配置", command=self.load_config).pack(side='left', padx=(10, 0))
        
        # 配置滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def toggle_optimization_params(self):
        """切换优化参数的可用状态"""
        state = 'normal' if self.enable_optimization_var.get() else 'disabled'
        self.opt_calls_spin.configure(state=state)
        
    def create_training_widgets(self):
        """创建训练控制界面"""
        # 训练控制区域
        control_frame = ttk.LabelFrame(self.train_frame, text="训练控制", padding=10)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        # 训练按钮
        self.start_train_btn = ttk.Button(
            control_frame, 
            text="开始训练", 
            command=self.start_training,
            style='Accent.TButton'
        )
        self.start_train_btn.pack(side='left')
        
        self.stop_train_btn = ttk.Button(
            control_frame, 
            text="停止训练", 
            command=self.stop_training,
            state='disabled'
        )
        self.stop_train_btn.pack(side='left', padx=(10, 0))
        
        # 训练状态
        self.train_status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(control_frame, textvariable=self.train_status_var, style='Status.TLabel')
        status_label.pack(side='right')
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(self.train_frame, text="训练进度", padding=10)
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        # 总体进度
        ttk.Label(progress_frame, text="总体进度:").pack(anchor='w')
        self.overall_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.overall_progress.pack(fill='x', pady=2)
        
        # 当前阶段进度
        ttk.Label(progress_frame, text="当前阶段:").pack(anchor='w', pady=(10, 0))
        self.current_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.current_progress.pack(fill='x', pady=2)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.train_frame, text="训练日志", padding=10)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=20, wrap='word')
        log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')
        
        # 清空日志按钮
        clear_log_btn = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_log_btn.pack(side='bottom', anchor='e', pady=(5, 0))

    def create_result_widgets(self):
        """创建结果查看界面"""
        # 结果概览区域
        overview_frame = ttk.LabelFrame(self.result_frame, text="训练结果概览", padding=10)
        overview_frame.pack(fill='x', padx=10, pady=5)

        # 创建指标显示网格
        metrics_frame = ttk.Frame(overview_frame)
        metrics_frame.pack(fill='x')

        # 评估指标
        self.metrics_vars = {
            'MSE': tk.StringVar(value="--"),
            'RMSE': tk.StringVar(value="--"),
            'MAE': tk.StringVar(value="--"),
            'MAPE': tk.StringVar(value="--"),
            'R2': tk.StringVar(value="--"),
            'Accuracy': tk.StringVar(value="--")
        }

        row = 0
        col = 0
        for metric, var in self.metrics_vars.items():
            ttk.Label(metrics_frame, text=f"{metric}:", font=('Microsoft YaHei', 10, 'bold')).grid(
                row=row, column=col*2, sticky='w', padx=5, pady=2
            )
            ttk.Label(metrics_frame, textvariable=var, style='Success.TLabel').grid(
                row=row, column=col*2+1, sticky='w', padx=5, pady=2
            )
            col += 1
            if col >= 3:
                col = 0
                row += 1

        # 图表显示区域
        chart_frame = ttk.LabelFrame(self.result_frame, text="可视化结果", padding=10)
        chart_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 创建图表选择
        chart_control_frame = ttk.Frame(chart_frame)
        chart_control_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(chart_control_frame, text="选择图表:").pack(side='left')
        self.chart_type_var = tk.StringVar(value="训练历史")
        chart_combo = ttk.Combobox(
            chart_control_frame,
            textvariable=self.chart_type_var,
            values=["训练历史", "预测对比", "误差分布", "特征重要性"],
            state='readonly'
        )
        chart_combo.pack(side='left', padx=(10, 0))
        chart_combo.bind('<<ComboboxSelected>>', self.update_chart)

        # 图表显示区域
        self.chart_frame = ttk.Frame(chart_frame)
        self.chart_frame.pack(fill='both', expand=True)

        # 结果导出区域
        export_frame = ttk.Frame(self.result_frame)
        export_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(export_frame, text="导出结果报告", command=self.export_report).pack(side='left')
        ttk.Button(export_frame, text="导出预测数据", command=self.export_predictions).pack(side='left', padx=(10, 0))
        ttk.Button(export_frame, text="保存模型", command=self.save_model).pack(side='left', padx=(10, 0))

    def create_prediction_widgets(self):
        """创建预测服务界面"""
        # 模型加载区域
        model_frame = ttk.LabelFrame(self.predict_frame, text="模型管理", padding=10)
        model_frame.pack(fill='x', padx=10, pady=5)

        # 模型文件选择
        ttk.Label(model_frame, text="模型文件:").pack(anchor='w')
        model_select_frame = ttk.Frame(model_frame)
        model_select_frame.pack(fill='x', pady=5)

        self.model_path_var = tk.StringVar()
        self.model_entry = ttk.Entry(model_select_frame, textvariable=self.model_path_var, width=60)
        self.model_entry.pack(side='left', fill='x', expand=True)

        ttk.Button(model_select_frame, text="浏览", command=self.browse_model, width=10).pack(side='right', padx=(5, 0))

        # 模型加载按钮
        model_btn_frame = ttk.Frame(model_frame)
        model_btn_frame.pack(fill='x', pady=5)

        self.load_model_btn = ttk.Button(model_btn_frame, text="加载模型", command=self.load_model)
        self.load_model_btn.pack(side='left')

        self.model_status_var = tk.StringVar(value="未加载模型")
        ttk.Label(model_btn_frame, textvariable=self.model_status_var, style='Status.TLabel').pack(side='right')

        # 预测输入区域
        input_frame = ttk.LabelFrame(self.predict_frame, text="预测输入", padding=10)
        input_frame.pack(fill='x', padx=10, pady=5)

        # 输入方式选择
        input_type_frame = ttk.Frame(input_frame)
        input_type_frame.pack(fill='x', pady=5)

        self.input_type_var = tk.StringVar(value="文件输入")
        ttk.Radiobutton(input_type_frame, text="文件输入", variable=self.input_type_var, value="文件输入").pack(side='left')
        ttk.Radiobutton(input_type_frame, text="手动输入", variable=self.input_type_var, value="手动输入").pack(side='left', padx=(20, 0))

        # 文件输入区域
        file_input_frame = ttk.Frame(input_frame)
        file_input_frame.pack(fill='x', pady=5)

        self.predict_file_var = tk.StringVar()
        ttk.Entry(file_input_frame, textvariable=self.predict_file_var, width=60).pack(side='left', fill='x', expand=True)
        ttk.Button(file_input_frame, text="选择文件", command=self.browse_predict_file, width=10).pack(side='right', padx=(5, 0))

        # 预测控制区域
        predict_control_frame = ttk.Frame(self.predict_frame)
        predict_control_frame.pack(fill='x', padx=10, pady=5)

        self.predict_btn = ttk.Button(
            predict_control_frame,
            text="开始预测",
            command=self.start_prediction,
            state='disabled'
        )
        self.predict_btn.pack(side='left')

        # 预测结果显示区域
        result_display_frame = ttk.LabelFrame(self.predict_frame, text="预测结果", padding=10)
        result_display_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 创建结果表格
        self.result_tree = ttk.Treeview(result_display_frame, columns=('时间', '预测值', '置信区间'), show='headings', height=15)
        self.result_tree.heading('时间', text='时间')
        self.result_tree.heading('预测值', text='预测值 (kW)')
        self.result_tree.heading('置信区间', text='置信区间')

        self.result_tree.column('时间', width=150)
        self.result_tree.column('预测值', width=100)
        self.result_tree.column('置信区间', width=150)

        self.result_tree.pack(side='left', fill='both', expand=True)

        result_scrollbar = ttk.Scrollbar(result_display_frame, orient='vertical', command=self.result_tree.yview)
        result_scrollbar.pack(side='right', fill='y')
        self.result_tree.configure(yscrollcommand=result_scrollbar.set)

    def create_status_bar(self):
        """创建状态栏"""
        # 系统状态
        self.system_status_var = tk.StringVar(value="系统就绪")
        ttk.Label(self.status_frame, textvariable=self.system_status_var).pack(side='left')

        # 分隔符
        ttk.Separator(self.status_frame, orient='vertical').pack(side='left', fill='y', padx=10)

        # GPU状态
        self.gpu_status_var = tk.StringVar(value="GPU: 检测中...")
        ttk.Label(self.status_frame, textvariable=self.gpu_status_var).pack(side='left')

        # 分隔符
        ttk.Separator(self.status_frame, orient='vertical').pack(side='left', fill='y', padx=10)

        # 内存使用
        self.memory_status_var = tk.StringVar(value="内存: --")
        ttk.Label(self.status_frame, textvariable=self.memory_status_var).pack(side='left')

        # 时间显示
        self.time_var = tk.StringVar()
        ttk.Label(self.status_frame, textvariable=self.time_var).pack(side='right')

        # 更新状态
        self.update_status()

    def setup_layout(self):
        """设置布局"""
        self.title_frame.pack(fill='x', padx=10, pady=5)
        self.title_label.pack()

        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        self.status_frame.pack(fill='x', side='bottom')

    def browse_file(self):
        """浏览数据文件"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_path_var.set(filename)
            self.load_btn.configure(state='normal')
            self.preview_data(filename)

    def preview_data(self, filename):
        """预览数据"""
        try:
            # 读取前几行数据进行预览
            if filename.endswith('.csv'):
                df = pd.read_csv(filename, nrows=100, encoding='utf-8-sig')
            else:
                df = pd.read_excel(filename, nrows=100)

            # 清空现有数据
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # 设置列
            self.data_tree['columns'] = list(df.columns)
            self.data_tree['show'] = 'headings'

            for col in df.columns:
                self.data_tree.heading(col, text=col)
                self.data_tree.column(col, width=100)

            # 插入数据
            for index, row in df.iterrows():
                self.data_tree.insert('', 'end', values=list(row))

            # 更新数据信息
            self.data_info_var.set(f"数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
            self.validate_btn.configure(state='normal')

        except Exception as e:
            messagebox.showerror("错误", f"预览数据失败: {str(e)}")
            self.data_info_var.set("数据预览失败")

    def load_data(self):
        """加载数据"""
        filename = self.file_path_var.get()
        if not filename:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        try:
            self.current_data = self.backend.load_data(filename)
            self.data_info_var.set(f"数据加载成功: {self.current_data.shape[0]} 行 × {self.current_data.shape[1]} 列")
            messagebox.showinfo("成功", "数据加载成功！")

            # 启用下一步操作
            self.notebook.tab(1, state='normal')  # 启用参数设置选项卡

        except Exception as e:
            messagebox.showerror("错误", f"数据加载失败: {str(e)}")

    def validate_data(self):
        """验证数据"""
        filename = self.file_path_var.get()
        if not filename:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        try:
            validation_result = self.backend.validate_data(filename)
            if validation_result['valid']:
                messagebox.showinfo("验证成功", "数据格式正确，可以进行训练！")
                self.load_btn.configure(state='normal')
            else:
                messagebox.showwarning("验证失败", f"数据验证失败:\n{validation_result['message']}")

        except Exception as e:
            messagebox.showerror("错误", f"数据验证失败: {str(e)}")

    def reset_parameters(self):
        """重置参数为默认值"""
        self.sequence_length_var.set(24)
        self.device_var.set("auto")
        self.hidden_size_var.set(128)
        self.num_layers_var.set(3)
        self.kernel_size_var.set(3)
        self.dropout_var.set(0.3)
        self.learning_rate_var.set(0.001)
        self.batch_size_var.set(64)
        self.enable_optimization_var.set(True)
        self.opt_calls_var.set(100)
        self.cv_folds_var.set(5)

    def save_config(self):
        """保存配置"""
        config = self.get_current_config()
        filename = filedialog.asksaveasfilename(
            title="保存配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)
                messagebox.showinfo("成功", "配置保存成功！")
            except Exception as e:
                messagebox.showerror("错误", f"配置保存失败: {str(e)}")

    def load_config(self):
        """加载配置"""
        filename = filedialog.askopenfilename(
            title="加载配置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.set_config(config)
                messagebox.showinfo("成功", "配置加载成功！")
            except Exception as e:
                messagebox.showerror("错误", f"配置加载失败: {str(e)}")

    def get_current_config(self):
        """获取当前配置"""
        return {
            'sequence_length': self.sequence_length_var.get(),
            'device': self.device_var.get(),
            'hidden_size': self.hidden_size_var.get(),
            'num_layers': self.num_layers_var.get(),
            'kernel_size': self.kernel_size_var.get(),
            'dropout': self.dropout_var.get(),
            'learning_rate': self.learning_rate_var.get(),
            'batch_size': self.batch_size_var.get(),
            'enable_optimization': self.enable_optimization_var.get(),
            'opt_calls': self.opt_calls_var.get(),
            'cv_folds': self.cv_folds_var.get()
        }

    def set_config(self, config):
        """设置配置"""
        self.sequence_length_var.set(config.get('sequence_length', 24))
        self.device_var.set(config.get('device', 'auto'))
        self.hidden_size_var.set(config.get('hidden_size', 128))
        self.num_layers_var.set(config.get('num_layers', 3))
        self.kernel_size_var.set(config.get('kernel_size', 3))
        self.dropout_var.set(config.get('dropout', 0.3))
        self.learning_rate_var.set(config.get('learning_rate', 0.001))
        self.batch_size_var.set(config.get('batch_size', 64))
        self.enable_optimization_var.set(config.get('enable_optimization', True))
        self.opt_calls_var.set(config.get('opt_calls', 100))
        self.cv_folds_var.set(config.get('cv_folds', 5))

    def start_training(self):
        """开始训练"""
        if self.current_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return

        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中")
            return

        # 获取配置
        config = self.get_current_config()

        # 更新界面状态
        self.is_training = True
        self.start_train_btn.configure(state='disabled')
        self.stop_train_btn.configure(state='normal')
        self.train_status_var.set("训练中...")

        # 清空日志
        self.clear_log()

        # 启动训练线程
        self.training_thread = threading.Thread(
            target=self._training_worker,
            args=(config,),
            daemon=True
        )
        self.training_thread.start()

    def _training_worker(self, config):
        """训练工作线程"""
        try:
            # 通过消息队列发送状态更新
            self.message_queue.put(('log', "开始训练..."))
            self.message_queue.put(('progress', 'overall', 10))

            # 调用后端训练
            result = self.backend.train_model(
                data=self.current_data,
                config=config,
                progress_callback=self._progress_callback,
                log_callback=self._log_callback
            )

            # 训练完成
            self.message_queue.put(('training_complete', result))

        except Exception as e:
            self.message_queue.put(('training_error', str(e)))

    def _progress_callback(self, stage, progress):
        """进度回调"""
        self.message_queue.put(('progress', stage, progress))

    def _log_callback(self, message):
        """日志回调"""
        self.message_queue.put(('log', message))

    def stop_training(self):
        """停止训练"""
        if self.training_thread and self.training_thread.is_alive():
            self.backend.stop_training()
            self.message_queue.put(('log', "正在停止训练..."))

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def update_chart(self, event=None):
        """更新图表显示"""
        chart_type = self.chart_type_var.get()

        # 清空现有图表
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        if not hasattr(self, 'training_results') or self.training_results is None:
            ttk.Label(self.chart_frame, text="暂无训练结果", style='Status.TLabel').pack(expand=True)
            return

        try:
            # 创建matplotlib图表
            fig, ax = plt.subplots(figsize=(10, 6))

            if chart_type == "训练历史":
                self._plot_training_history(ax)
            elif chart_type == "预测对比":
                self._plot_prediction_comparison(ax)
            elif chart_type == "误差分布":
                self._plot_error_distribution(ax)
            elif chart_type == "特征重要性":
                self._plot_feature_importance(ax)

            # 嵌入到tkinter中
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            ttk.Label(self.chart_frame, text=f"图表显示错误: {str(e)}", style='Error.TLabel').pack(expand=True)

    def _plot_training_history(self, ax):
        """绘制训练历史"""
        if 'history' in self.training_results:
            history = self.training_results['history']
            ax.plot(history['train_loss'], label='训练损失')
            ax.plot(history['val_loss'], label='验证损失')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('损失')
            ax.set_title('训练历史')
            ax.legend()
            ax.grid(True)

    def _plot_prediction_comparison(self, ax):
        """绘制预测对比"""
        if 'predictions' in self.training_results and 'actuals' in self.training_results:
            predictions = self.training_results['predictions']
            actuals = self.training_results['actuals']
            ax.plot(actuals[:100], label='实际值', alpha=0.7)
            ax.plot(predictions[:100], label='预测值', alpha=0.7)
            ax.set_xlabel('样本')
            ax.set_ylabel('充电负荷 (kW)')
            ax.set_title('预测对比 (前100个样本)')
            ax.legend()
            ax.grid(True)

    def _plot_error_distribution(self, ax):
        """绘制误差分布"""
        if 'predictions' in self.training_results and 'actuals' in self.training_results:
            predictions = self.training_results['predictions']
            actuals = self.training_results['actuals']
            errors = predictions - actuals
            ax.hist(errors, bins=50, alpha=0.7, edgecolor='black')
            ax.set_xlabel('预测误差')
            ax.set_ylabel('频数')
            ax.set_title('预测误差分布')
            ax.grid(True)

    def _plot_feature_importance(self, ax):
        """绘制特征重要性"""
        if 'feature_importance' in self.training_results:
            importance = self.training_results['feature_importance']
            features = list(importance.keys())
            values = list(importance.values())
            ax.barh(features, values)
            ax.set_xlabel('重要性得分')
            ax.set_title('特征重要性')
            ax.grid(True)

    def export_report(self):
        """导出结果报告"""
        if not hasattr(self, 'training_results') or self.training_results is None:
            messagebox.showwarning("警告", "暂无训练结果")
            return

        filename = filedialog.asksaveasfilename(
            title="导出结果报告",
            defaultextension=".html",
            filetypes=[("HTML文件", "*.html"), ("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                self.backend.export_report(self.training_results, filename)
                messagebox.showinfo("成功", "报告导出成功！")
            except Exception as e:
                messagebox.showerror("错误", f"报告导出失败: {str(e)}")

    def export_predictions(self):
        """导出预测数据"""
        if not hasattr(self, 'training_results') or self.training_results is None:
            messagebox.showwarning("警告", "暂无训练结果")
            return

        filename = filedialog.asksaveasfilename(
            title="导出预测数据",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                self.backend.export_predictions(self.training_results, filename)
                messagebox.showinfo("成功", "预测数据导出成功！")
            except Exception as e:
                messagebox.showerror("错误", f"预测数据导出失败: {str(e)}")

    def save_model(self):
        """保存模型"""
        if not hasattr(self, 'training_results') or self.training_results is None:
            messagebox.showwarning("警告", "暂无训练结果")
            return

        filename = filedialog.asksaveasfilename(
            title="保存模型",
            defaultextension=".pth",
            filetypes=[("PyTorch模型", "*.pth"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                self.backend.save_model(self.training_results['model'], filename)
                messagebox.showinfo("成功", "模型保存成功！")
            except Exception as e:
                messagebox.showerror("错误", f"模型保存失败: {str(e)}")

    def browse_model(self):
        """浏览模型文件"""
        filename = filedialog.askopenfilename(
            title="选择模型文件",
            filetypes=[("PyTorch模型", "*.pth"), ("所有文件", "*.*")]
        )
        if filename:
            self.model_path_var.set(filename)
            self.load_model_btn.configure(state='normal')

    def load_model(self):
        """加载模型"""
        model_path = self.model_path_var.get()
        if not model_path:
            messagebox.showwarning("警告", "请先选择模型文件")
            return

        try:
            self.current_model = self.backend.load_model(model_path)
            self.model_status_var.set("模型加载成功")
            self.predict_btn.configure(state='normal')
            messagebox.showinfo("成功", "模型加载成功！")
        except Exception as e:
            messagebox.showerror("错误", f"模型加载失败: {str(e)}")
            self.model_status_var.set("模型加载失败")

    def browse_predict_file(self):
        """浏览预测文件"""
        filename = filedialog.askopenfilename(
            title="选择预测数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.predict_file_var.set(filename)

    def start_prediction(self):
        """开始预测"""
        if self.current_model is None:
            messagebox.showwarning("警告", "请先加载模型")
            return

        predict_file = self.predict_file_var.get()
        if not predict_file:
            messagebox.showwarning("警告", "请选择预测数据文件")
            return

        try:
            # 执行预测
            predictions = self.backend.predict(self.current_model, predict_file)

            # 清空现有结果
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)

            # 显示预测结果
            for i, pred in enumerate(predictions):
                timestamp = pred.get('timestamp', f'样本 {i+1}')
                value = f"{pred['value']:.2f}"
                confidence = f"±{pred.get('confidence', 0):.2f}"
                self.result_tree.insert('', 'end', values=(timestamp, value, confidence))

            messagebox.showinfo("成功", f"预测完成！共生成 {len(predictions)} 个预测结果")

        except Exception as e:
            messagebox.showerror("错误", f"预测失败: {str(e)}")

    def update_status(self):
        """更新状态栏"""
        # 更新时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)

        # 更新GPU状态
        try:
            gpu_info = self.backend.get_gpu_info()
            self.gpu_status_var.set(f"GPU: {gpu_info}")
        except:
            self.gpu_status_var.set("GPU: 不可用")

        # 更新内存状态
        try:
            memory_info = self.backend.get_memory_info()
            self.memory_status_var.set(f"内存: {memory_info}")
        except:
            self.memory_status_var.set("内存: --")

        # 定时更新
        self.root.after(5000, self.update_status)

    def process_messages(self):
        """处理消息队列"""
        try:
            while True:
                message_type, *args = self.message_queue.get_nowait()

                if message_type == 'log':
                    self._add_log(args[0])
                elif message_type == 'progress':
                    self._update_progress(args[0], args[1])
                elif message_type == 'training_complete':
                    self._training_complete(args[0])
                elif message_type == 'training_error':
                    self._training_error(args[0])

        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.process_messages)

    def _add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

    def _update_progress(self, stage, progress):
        """更新进度"""
        if stage == 'overall':
            self.overall_progress['value'] = progress
        elif stage == 'current':
            self.current_progress['value'] = progress

    def _training_complete(self, result):
        """训练完成处理"""
        self.training_results = result
        self.is_training = False
        self.start_train_btn.configure(state='normal')
        self.stop_train_btn.configure(state='disabled')
        self.train_status_var.set("训练完成")

        # 更新评估指标
        if 'metrics' in result:
            metrics = result['metrics']
            for key, var in self.metrics_vars.items():
                if key.lower() in metrics:
                    value = metrics[key.lower()]
                    if isinstance(value, float):
                        var.set(f"{value:.4f}")
                    else:
                        var.set(str(value))

        # 启用结果查看选项卡
        self.notebook.tab(3, state='normal')
        self.notebook.select(3)

        # 更新图表
        self.update_chart()

        messagebox.showinfo("成功", "模型训练完成！")

    def _training_error(self, error_message):
        """训练错误处理"""
        self.is_training = False
        self.start_train_btn.configure(state='normal')
        self.stop_train_btn.configure(state='disabled')
        self.train_status_var.set("训练失败")

        messagebox.showerror("训练错误", f"训练过程中发生错误:\n{error_message}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = EVChargingPredictionGUI()
    app.run()
