#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI组件模块 - 提供可重用的界面组件
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

class ParameterFrame(ttk.LabelFrame):
    """参数设置框架"""
    
    def __init__(self, parent, title="参数设置", **kwargs):
        super().__init__(parent, text=title, padding=10, **kwargs)
        self.parameters = {}
        self.row_count = 0
        
    def add_parameter(self, name, param_type, default_value, **options):
        """添加参数控件
        
        Args:
            name: 参数名称
            param_type: 参数类型 ('int', 'float', 'str', 'bool', 'choice')
            default_value: 默认值
            **options: 其他选项 (min_val, max_val, choices等)
        """
        ttk.Label(self, text=f"{name}:").grid(row=self.row_count, column=0, sticky='w', pady=2)
        
        if param_type == 'int':
            var = tk.IntVar(value=default_value)
            widget = ttk.Spinbox(
                self, 
                from_=options.get('min_val', 0),
                to=options.get('max_val', 1000),
                increment=options.get('increment', 1),
                textvariable=var,
                width=12
            )
        elif param_type == 'float':
            var = tk.DoubleVar(value=default_value)
            widget = ttk.Spinbox(
                self,
                from_=options.get('min_val', 0.0),
                to=options.get('max_val', 1.0),
                increment=options.get('increment', 0.1),
                textvariable=var,
                width=12
            )
        elif param_type == 'str':
            var = tk.StringVar(value=default_value)
            widget = ttk.Entry(self, textvariable=var, width=12)
        elif param_type == 'bool':
            var = tk.BooleanVar(value=default_value)
            widget = ttk.Checkbutton(self, variable=var)
        elif param_type == 'choice':
            var = tk.StringVar(value=default_value)
            widget = ttk.Combobox(
                self,
                textvariable=var,
                values=options.get('choices', []),
                state='readonly',
                width=12
            )
        else:
            raise ValueError(f"不支持的参数类型: {param_type}")
            
        widget.grid(row=self.row_count, column=1, sticky='w', padx=(10, 0), pady=2)
        
        if 'unit' in options:
            ttk.Label(self, text=options['unit']).grid(
                row=self.row_count, column=2, sticky='w', padx=(5, 0), pady=2
            )
            
        self.parameters[name] = {
            'var': var,
            'widget': widget,
            'type': param_type
        }
        
        self.row_count += 1
        
    def get_values(self):
        """获取所有参数值"""
        values = {}
        for name, param in self.parameters.items():
            values[name] = param['var'].get()
        return values
        
    def set_values(self, values):
        """设置参数值"""
        for name, value in values.items():
            if name in self.parameters:
                self.parameters[name]['var'].set(value)
                
    def reset_to_defaults(self):
        """重置为默认值"""
        for name, param in self.parameters.items():
            if hasattr(param['widget'], 'default_value'):
                param['var'].set(param['widget'].default_value)

class ProgressPanel(ttk.LabelFrame):
    """进度显示面板"""
    
    def __init__(self, parent, title="进度", **kwargs):
        super().__init__(parent, text=title, padding=10, **kwargs)
        self.create_widgets()
        
    def create_widgets(self):
        """创建进度控件"""
        # 总体进度
        ttk.Label(self, text="总体进度:").pack(anchor='w')
        self.overall_progress = ttk.Progressbar(self, mode='determinate')
        self.overall_progress.pack(fill='x', pady=(2, 10))
        
        # 当前阶段进度
        ttk.Label(self, text="当前阶段:").pack(anchor='w')
        self.current_progress = ttk.Progressbar(self, mode='determinate')
        self.current_progress.pack(fill='x', pady=2)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self, textvariable=self.status_var)
        self.status_label.pack(anchor='w', pady=(5, 0))
        
    def update_overall_progress(self, value):
        """更新总体进度"""
        self.overall_progress['value'] = value
        
    def update_current_progress(self, value):
        """更新当前阶段进度"""
        self.current_progress['value'] = value
        
    def set_status(self, status):
        """设置状态文本"""
        self.status_var.set(status)
        
    def reset(self):
        """重置进度"""
        self.overall_progress['value'] = 0
        self.current_progress['value'] = 0
        self.status_var.set("就绪")

class LogPanel(ttk.LabelFrame):
    """日志显示面板"""
    
    def __init__(self, parent, title="日志", height=15, **kwargs):
        super().__init__(parent, text=title, padding=10, **kwargs)
        self.create_widgets(height)
        
    def create_widgets(self, height):
        """创建日志控件"""
        # 创建文本框和滚动条
        self.text_widget = tk.Text(self, height=height, wrap='word')
        self.scrollbar = ttk.Scrollbar(self, orient='vertical', command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局
        self.text_widget.pack(side='left', fill='both', expand=True)
        self.scrollbar.pack(side='right', fill='y')
        
        # 清空按钮
        self.clear_btn = ttk.Button(self, text="清空日志", command=self.clear)
        self.clear_btn.pack(side='bottom', anchor='e', pady=(5, 0))
        
    def add_log(self, message, level='INFO'):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        color_map = {
            'INFO': 'black',
            'WARNING': 'orange',
            'ERROR': 'red',
            'SUCCESS': 'green'
        }
        
        log_message = f"[{timestamp}] [{level}] {message}\n"
        
        self.text_widget.insert(tk.END, log_message)
        self.text_widget.see(tk.END)
        
        # 设置颜色（如果需要）
        if level in color_map:
            start_line = self.text_widget.index(tk.END + "-2l linestart")
            end_line = self.text_widget.index(tk.END + "-1l lineend")
            tag_name = f"level_{level}"
            self.text_widget.tag_add(tag_name, start_line, end_line)
            self.text_widget.tag_config(tag_name, foreground=color_map[level])
            
    def clear(self):
        """清空日志"""
        self.text_widget.delete(1.0, tk.END)

class MetricsPanel(ttk.LabelFrame):
    """指标显示面板"""
    
    def __init__(self, parent, title="评估指标", **kwargs):
        super().__init__(parent, text=title, padding=10, **kwargs)
        self.metrics = {}
        self.create_widgets()
        
    def create_widgets(self):
        """创建指标显示控件"""
        self.metrics_frame = ttk.Frame(self)
        self.metrics_frame.pack(fill='x')
        
        # 预定义的指标
        default_metrics = [
            ('MSE', '均方误差'),
            ('RMSE', '均方根误差'),
            ('MAE', '平均绝对误差'),
            ('MAPE', '平均绝对百分比误差'),
            ('R²', '决定系数'),
            ('准确率', '预测准确率')
        ]
        
        row = 0
        col = 0
        for metric_key, metric_name in default_metrics:
            self.add_metric(metric_key, metric_name, row, col)
            col += 1
            if col >= 3:
                col = 0
                row += 1
                
    def add_metric(self, key, name, row=0, col=0):
        """添加指标显示"""
        ttk.Label(
            self.metrics_frame, 
            text=f"{name}:", 
            font=('Microsoft YaHei', 10, 'bold')
        ).grid(row=row, column=col*2, sticky='w', padx=5, pady=2)
        
        var = tk.StringVar(value="--")
        label = ttk.Label(
            self.metrics_frame, 
            textvariable=var,
            foreground='blue',
            font=('Microsoft YaHei', 10)
        )
        label.grid(row=row, column=col*2+1, sticky='w', padx=5, pady=2)
        
        self.metrics[key] = var
        
    def update_metric(self, key, value):
        """更新指标值"""
        if key in self.metrics:
            if isinstance(value, float):
                if key == 'MAPE' or key == '准确率':
                    self.metrics[key].set(f"{value:.2f}%")
                else:
                    self.metrics[key].set(f"{value:.4f}")
            else:
                self.metrics[key].set(str(value))
                
    def update_all_metrics(self, metrics_dict):
        """批量更新指标"""
        for key, value in metrics_dict.items():
            self.update_metric(key, value)
            
    def reset_metrics(self):
        """重置所有指标"""
        for var in self.metrics.values():
            var.set("--")

class ChartPanel(ttk.LabelFrame):
    """图表显示面板"""
    
    def __init__(self, parent, title="图表", **kwargs):
        super().__init__(parent, text=title, padding=10, **kwargs)
        self.current_chart = None
        self.create_widgets()
        
    def create_widgets(self):
        """创建图表控件"""
        # 图表类型选择
        control_frame = ttk.Frame(self)
        control_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(control_frame, text="图表类型:").pack(side='left')
        
        self.chart_type_var = tk.StringVar(value="训练历史")
        self.chart_combo = ttk.Combobox(
            control_frame,
            textvariable=self.chart_type_var,
            values=["训练历史", "预测对比", "误差分布", "特征重要性"],
            state='readonly'
        )
        self.chart_combo.pack(side='left', padx=(10, 0))
        self.chart_combo.bind('<<ComboboxSelected>>', self.on_chart_type_changed)
        
        # 图表显示区域
        self.chart_frame = ttk.Frame(self)
        self.chart_frame.pack(fill='both', expand=True)
        
    def on_chart_type_changed(self, event=None):
        """图表类型改变事件"""
        # 子类可以重写此方法
        pass
        
    def clear_chart(self):
        """清空图表"""
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
            
    def show_message(self, message):
        """显示消息"""
        self.clear_chart()
        ttk.Label(self.chart_frame, text=message).pack(expand=True)
        
    def plot_chart(self, plot_func, *args, **kwargs):
        """绘制图表"""
        try:
            self.clear_chart()
            
            # 创建matplotlib图表
            fig = Figure(figsize=(10, 6), dpi=100)
            ax = fig.add_subplot(111)
            
            # 调用绘图函数
            plot_func(ax, *args, **kwargs)
            
            # 嵌入到tkinter中
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
            
            self.current_chart = canvas
            
        except Exception as e:
            self.show_message(f"图表显示错误: {str(e)}")

class FileSelector(ttk.Frame):
    """文件选择器组件"""
    
    def __init__(self, parent, label_text="选择文件:", file_types=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.file_types = file_types or [("所有文件", "*.*")]
        self.create_widgets(label_text)
        
    def create_widgets(self, label_text):
        """创建文件选择控件"""
        ttk.Label(self, text=label_text).pack(anchor='w')
        
        file_frame = ttk.Frame(self)
        file_frame.pack(fill='x', pady=5)
        
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var)
        self.file_entry.pack(side='left', fill='x', expand=True)
        
        self.browse_btn = ttk.Button(
            file_frame,
            text="浏览",
            command=self.browse_file,
            width=10
        )
        self.browse_btn.pack(side='right', padx=(5, 0))
        
    def browse_file(self):
        """浏览文件"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="选择文件",
            filetypes=self.file_types
        )
        if filename:
            self.file_var.set(filename)
            
    def get_file_path(self):
        """获取文件路径"""
        return self.file_var.get()
        
    def set_file_path(self, path):
        """设置文件路径"""
        self.file_var.set(path)

class StatusBar(ttk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.status_vars = {}
        self.create_widgets()
        
    def create_widgets(self):
        """创建状态栏控件"""
        # 主状态
        self.main_status_var = tk.StringVar(value="就绪")
        ttk.Label(self, textvariable=self.main_status_var).pack(side='left')
        
    def add_status_item(self, name, initial_value="--"):
        """添加状态项"""
        # 分隔符
        ttk.Separator(self, orient='vertical').pack(side='left', fill='y', padx=10)
        
        # 状态标签
        var = tk.StringVar(value=initial_value)
        ttk.Label(self, textvariable=var).pack(side='left')
        
        self.status_vars[name] = var
        
    def update_status(self, name, value):
        """更新状态"""
        if name == 'main':
            self.main_status_var.set(value)
        elif name in self.status_vars:
            self.status_vars[name].set(value)
            
    def set_main_status(self, status):
        """设置主状态"""
        self.main_status_var.set(status)
