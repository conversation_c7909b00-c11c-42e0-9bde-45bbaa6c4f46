#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示数据生成器 - 为GUI测试生成模拟的电动汽车充电负荷数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def generate_demo_data(
    start_date='2023-01-01',
    end_date='2023-12-31',
    output_file='demo_charging_data.csv'
):
    """生成演示用的电动汽车充电负荷数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        output_file: 输出文件名
    """
    
    # 创建时间序列
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    # 生成每小时的时间戳
    timestamps = []
    current = start
    while current <= end:
        timestamps.append(current)
        current += timedelta(hours=1)
    
    n_samples = len(timestamps)
    print(f"生成 {n_samples} 个数据点 ({start_date} 到 {end_date})")
    
    # 生成基础负荷模式
    # 1. 日周期模式 (24小时)
    hours = np.array([t.hour for t in timestamps])
    daily_pattern = 30 + 40 * np.sin(2 * np.pi * (hours - 6) / 24)  # 早晚高峰
    
    # 2. 周周期模式 (7天)
    weekdays = np.array([t.weekday() for t in timestamps])
    weekly_pattern = np.where(weekdays < 5, 1.2, 0.8)  # 工作日vs周末
    
    # 3. 季节性模式 (年周期)
    day_of_year = np.array([t.timetuple().tm_yday for t in timestamps])
    seasonal_pattern = 1 + 0.3 * np.sin(2 * np.pi * (day_of_year - 80) / 365)  # 夏季高峰
    
    # 4. 随机噪声
    np.random.seed(42)  # 确保可重现
    noise = np.random.normal(0, 5, n_samples)
    
    # 5. 异常值 (模拟特殊事件)
    anomaly_indices = np.random.choice(n_samples, size=int(n_samples * 0.02), replace=False)
    anomaly_multiplier = np.ones(n_samples)
    anomaly_multiplier[anomaly_indices] = np.random.uniform(1.5, 2.5, len(anomaly_indices))
    
    # 合成充电负荷
    charging_load = (daily_pattern * weekly_pattern * seasonal_pattern + noise) * anomaly_multiplier
    charging_load = np.maximum(charging_load, 5)  # 确保最小值为5kW
    
    # 生成天气数据
    # 气温 (与季节相关)
    base_temp = 15 + 10 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
    daily_temp_variation = 5 * np.sin(2 * np.pi * hours / 24)
    temperature_noise = np.random.normal(0, 2, n_samples)
    avg_temperature = base_temp + daily_temp_variation + temperature_noise
    
    min_temperature = avg_temperature - np.random.uniform(3, 8, n_samples)
    max_temperature = avg_temperature + np.random.uniform(3, 8, n_samples)
    
    # 降水量 (随机，夏季稍多)
    precipitation_prob = 0.1 + 0.05 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
    has_rain = np.random.random(n_samples) < precipitation_prob
    precipitation = np.where(has_rain, np.random.exponential(5, n_samples), 0)
    
    # 创建DataFrame
    data = pd.DataFrame({
        '充电时间': timestamps,
        '总有功功率_总和(kW)': np.round(charging_load, 2),
        '降水量(mm)': np.round(precipitation, 1),
        '平均气温(℃)': np.round(avg_temperature, 1),
        '最低气温(℃)': np.round(min_temperature, 1),
        '最高气温(℃)': np.round(max_temperature, 1)
    })
    
    # 添加一些衍生特征
    data['小时'] = data['充电时间'].dt.hour
    data['星期几'] = data['充电时间'].dt.weekday
    data['月份'] = data['充电时间'].dt.month
    data['是否工作日'] = (data['星期几'] < 5).astype(int)
    
    # 保存数据
    data.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"演示数据已保存到: {output_file}")
    
    # 显示数据统计信息
    print("\n数据统计信息:")
    print(f"时间范围: {data['充电时间'].min()} 到 {data['充电时间'].max()}")
    print(f"充电负荷范围: {data['总有功功率_总和(kW)'].min():.2f} - {data['总有功功率_总和(kW)'].max():.2f} kW")
    print(f"平均充电负荷: {data['总有功功率_总和(kW)'].mean():.2f} kW")
    print(f"气温范围: {data['平均气温(℃)'].min():.1f} - {data['平均气温(℃)'].max():.1f} ℃")
    print(f"降水天数: {(data['降水量(mm)'] > 0).sum()} 天")
    
    return data

def generate_small_demo_data(output_file='small_demo_data.csv'):
    """生成小规模演示数据 (用于快速测试)"""
    return generate_demo_data(
        start_date='2023-11-01',
        end_date='2023-11-30',
        output_file=output_file
    )

def generate_prediction_demo_data(output_file='prediction_demo_data.csv'):
    """生成用于预测的演示数据"""
    # 生成未来一周的数据
    start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    end = start + timedelta(days=7)
    
    timestamps = []
    current = start
    while current < end:
        timestamps.append(current)
        current += timedelta(hours=1)
    
    n_samples = len(timestamps)
    
    # 生成简单的模式数据
    hours = np.array([t.hour for t in timestamps])
    daily_pattern = 30 + 40 * np.sin(2 * np.pi * (hours - 6) / 24)
    
    # 添加一些随机性
    np.random.seed(123)
    noise = np.random.normal(0, 3, n_samples)
    charging_load = daily_pattern + noise
    charging_load = np.maximum(charging_load, 5)
    
    # 生成天气数据
    avg_temp = 20 + np.random.normal(0, 5, n_samples)
    min_temp = avg_temp - np.random.uniform(2, 5, n_samples)
    max_temp = avg_temp + np.random.uniform(2, 5, n_samples)
    precipitation = np.where(np.random.random(n_samples) < 0.1, 
                           np.random.exponential(3, n_samples), 0)
    
    data = pd.DataFrame({
        '充电时间': timestamps,
        '总有功功率_总和(kW)': np.round(charging_load, 2),
        '降水量(mm)': np.round(precipitation, 1),
        '平均气温(℃)': np.round(avg_temp, 1),
        '最低气温(℃)': np.round(min_temp, 1),
        '最高气温(℃)': np.round(max_temp, 1)
    })
    
    data.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"预测演示数据已保存到: {output_file}")
    
    return data

def main():
    """主函数"""
    print("电动汽车充电负荷演示数据生成器")
    print("=" * 50)
    
    # 生成不同规模的演示数据
    print("1. 生成完整年度数据...")
    generate_demo_data()
    
    print("\n2. 生成小规模测试数据...")
    generate_small_demo_data()
    
    print("\n3. 生成预测演示数据...")
    generate_prediction_demo_data()
    
    print("\n演示数据生成完成！")
    print("\n使用说明:")
    print("- demo_charging_data.csv: 完整年度数据，用于模型训练")
    print("- small_demo_data.csv: 小规模数据，用于快速测试")
    print("- prediction_demo_data.csv: 预测数据，用于测试预测功能")
    
    print("\n您可以在GUI中使用这些文件来测试系统功能。")

if __name__ == "__main__":
    main()
