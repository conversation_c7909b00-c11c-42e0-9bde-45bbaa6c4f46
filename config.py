#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理系统
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class ModelConfig:
    """模型配置"""
    input_size: int = 1
    hidden_size: int = 128
    num_layers: int = 3
    output_size: int = 1
    kernel_size: int = 3
    dropout: float = 0.3
    gru_layers: int = 1
    l2_reg: float = 1e-4
    bidirectional: bool = True

@dataclass
class TrainingConfig:
    """训练配置"""
    sequence_length: int = 24
    batch_size: int = 64
    learning_rate: float = 0.001
    epochs: int = 400
    device: str = "auto"
    optimizer_type: int = 0  # 0: Adam, 1: AdamW, 2: RAdam
    loss_type: str = "MSELoss"
    early_stopping_patience: int = 50
    cv_folds: int = 5

@dataclass
class OptimizationConfig:
    """优化配置"""
    enable_bayesian_optimization: bool = False
    n_calls: int = 100
    enable_multi_objective: bool = True
    enable_uncertainty_quantification: bool = True
    random_state: int = 42

@dataclass
class DataConfig:
    """数据配置"""
    data_file: str = ""
    test_size: float = 0.2
    validation_size: float = 0.2
    target_column: str = "Charging_Load_kW"
    timestamp_column: str = "Timestamp"
    feature_columns: list = None
    
    def __post_init__(self):
        if self.feature_columns is None:
            self.feature_columns = [
                "Precipitation_mm", "Average_Temperature_C",
                "Min_Temperature_C", "Max_Temperature_C"
            ]

@dataclass
class SystemConfig:
    """系统配置"""
    log_level: str = "INFO"
    output_dir: str = "outputs"
    model_save_dir: str = "models"
    results_save_dir: str = "results"
    enable_gpu: bool = True
    random_seed: int = 42

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.json"
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.optimization = OptimizationConfig()
        self.data = DataConfig()
        self.system = SystemConfig()
        
        # 创建必要的目录
        self._create_directories()
        
        # 如果配置文件存在，加载配置
        if os.path.exists(self.config_file):
            self.load_config()
    
    def _create_directories(self):
        """创建必要的目录"""
        dirs = [
            self.system.output_dir,
            self.system.model_save_dir,
            self.system.results_save_dir
        ]
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def load_config(self, config_file: Optional[str] = None):
        """加载配置文件"""
        file_path = config_file or self.config_file
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新各个配置对象
            if 'model' in config_data:
                self.model = ModelConfig(**config_data['model'])
            if 'training' in config_data:
                self.training = TrainingConfig(**config_data['training'])
            if 'optimization' in config_data:
                self.optimization = OptimizationConfig(**config_data['optimization'])
            if 'data' in config_data:
                self.data = DataConfig(**config_data['data'])
            if 'system' in config_data:
                self.system = SystemConfig(**config_data['system'])
            
            logging.info(f"配置已从 {file_path} 加载")
            
        except Exception as e:
            logging.warning(f"加载配置文件失败: {e}，使用默认配置")
    
    def save_config(self, config_file: Optional[str] = None):
        """保存配置文件"""
        file_path = config_file or self.config_file
        
        config_data = {
            'model': asdict(self.model),
            'training': asdict(self.training),
            'optimization': asdict(self.optimization),
            'data': asdict(self.data),
            'system': asdict(self.system)
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            
            logging.info(f"配置已保存到 {file_path}")
            
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置字典"""
        return asdict(self.model)
    
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置字典"""
        config = asdict(self.training)
        
        # 处理设备自动选择
        if config['device'] == 'auto':
            import torch
            config['device'] = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        return config
    
    def get_optimization_config(self) -> Dict[str, Any]:
        """获取优化配置字典"""
        return asdict(self.optimization)
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置字典"""
        return asdict(self.data)
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置字典"""
        return asdict(self.system)
    
    def update_model_config(self, **kwargs):
        """更新模型配置"""
        for key, value in kwargs.items():
            if hasattr(self.model, key):
                setattr(self.model, key, value)
    
    def update_training_config(self, **kwargs):
        """更新训练配置"""
        for key, value in kwargs.items():
            if hasattr(self.training, key):
                setattr(self.training, key, value)
    
    def update_optimization_config(self, **kwargs):
        """更新优化配置"""
        for key, value in kwargs.items():
            if hasattr(self.optimization, key):
                setattr(self.optimization, key, value)
    
    def update_data_config(self, **kwargs):
        """更新数据配置"""
        for key, value in kwargs.items():
            if hasattr(self.data, key):
                setattr(self.data, key, value)
    
    def update_system_config(self, **kwargs):
        """更新系统配置"""
        for key, value in kwargs.items():
            if hasattr(self.system, key):
                setattr(self.system, key, value)
    
    def get_full_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return {
            'model': self.get_model_config(),
            'training': self.get_training_config(),
            'optimization': self.get_optimization_config(),
            'data': self.get_data_config(),
            'system': self.get_system_config()
        }
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.optimization = OptimizationConfig()
        self.data = DataConfig()
        self.system = SystemConfig()
        
        logging.info("配置已重置为默认值")

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """获取全局配置管理器"""
    return config_manager

def load_config(config_file: str):
    """加载指定的配置文件"""
    config_manager.load_config(config_file)

def save_config(config_file: str):
    """保存配置到指定文件"""
    config_manager.save_config(config_file)

# 快速访问函数
def get_model_config() -> Dict[str, Any]:
    """快速获取模型配置"""
    return config_manager.get_model_config()

def get_training_config() -> Dict[str, Any]:
    """快速获取训练配置"""
    return config_manager.get_training_config()

def get_optimization_config() -> Dict[str, Any]:
    """快速获取优化配置"""
    return config_manager.get_optimization_config()

def get_data_config() -> Dict[str, Any]:
    """快速获取数据配置"""
    return config_manager.get_data_config()

def get_system_config() -> Dict[str, Any]:
    """快速获取系统配置"""
    return config_manager.get_system_config()
