# 电动汽车充电负荷预测系统 - GUI版本

## 概述

本系统将原本复杂的命令行预测模型转化为直观的图形化操作界面，用户无需深入理解底层算法，即可轻松完成数据导入、模型选择、预测启动、结果查询等核心任务。

## 主要功能

### 🔧 核心功能
- **数据导入**: 支持CSV、Excel格式的数据文件导入和预览
- **参数设置**: 直观的参数配置界面，支持保存和加载配置
- **模型训练**: 一键启动训练，实时显示进度和日志
- **结果查看**: 多种图表展示训练结果和评估指标
- **预测服务**: 使用训练好的模型进行新数据预测
- **数据可视化**: 训练历史、预测对比、误差分布等图表

### 🎯 技术特点
- **IBOTCNGRU模型**: 集成时间卷积网络(TCN)、GRU和注意力机制
- **贝叶斯优化**: 自动超参数优化，提升模型性能
- **交叉验证**: 10折时间序列交叉验证，确保模型稳定性
- **多线程处理**: 异步训练，界面不卡顿
- **实时监控**: 系统资源和训练状态实时显示

## 安装和运行

### 1. 环境要求
- Python 3.7+
- PyTorch 1.8+
- 其他依赖包（见requirements.txt）

### 2. 安装依赖
```bash
pip install torch pandas numpy matplotlib scikit-learn psutil tkinter
```

### 3. 生成演示数据（可选）
```bash
python generate_demo_data.py
```
这将生成三个演示数据文件：
- `demo_charging_data.csv`: 完整年度数据
- `small_demo_data.csv`: 小规模测试数据  
- `prediction_demo_data.csv`: 预测演示数据

### 4. 启动GUI
```bash
python run_gui.py
```

## 使用指南

### 第一步：数据导入
1. 点击"数据导入"选项卡
2. 点击"浏览"按钮选择数据文件
3. 系统会自动预览数据内容
4. 点击"验证数据"检查数据格式
5. 点击"加载数据"完成数据导入

**数据格式要求：**
- 必须包含时间列：`充电时间` 或 `Timestamp`
- 必须包含负荷列：`总有功功率_总和(kW)` 或 `Charging_Load_kW`
- 可选天气列：`降水量(mm)`、`平均气温(℃)`等

### 第二步：参数设置
1. 点击"参数设置"选项卡
2. 根据需要调整以下参数：
   - **序列长度**: 输入序列的时间步长（默认24小时）
   - **计算设备**: 选择CPU或GPU
   - **模型参数**: 隐藏层大小、网络层数等
   - **训练参数**: 学习率、批次大小等
   - **优化参数**: 是否启用贝叶斯优化
3. 可以保存配置供下次使用

### 第三步：模型训练
1. 点击"模型训练"选项卡
2. 点击"开始训练"按钮
3. 观察训练进度和日志输出
4. 训练完成后自动跳转到结果页面

**训练过程：**
- 数据预处理和特征工程
- 超参数优化（如果启用）
- 交叉验证训练
- 模型评估和保存

### 第四步：结果查看
1. 训练完成后查看评估指标：
   - MSE、RMSE、MAE、MAPE、R²等
2. 切换不同图表类型：
   - 训练历史：损失函数变化
   - 预测对比：实际值vs预测值
   - 误差分布：预测误差统计
   - 特征重要性：各特征的贡献度
3. 导出功能：
   - 导出结果报告（HTML格式）
   - 导出预测数据（CSV/Excel）
   - 保存训练好的模型

### 第五步：预测服务
1. 点击"预测服务"选项卡
2. 加载训练好的模型文件
3. 选择要预测的数据文件
4. 点击"开始预测"
5. 查看预测结果表格

## 界面说明

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│                电动汽车充电负荷预测系统                    │
├─────────────────────────────────────────────────────────┤
│ [数据导入] [参数设置] [模型训练] [结果查看] [预测服务]      │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    主要内容区域                          │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ 系统就绪 | GPU: RTX 3080 | 内存: 8.5GB/16GB | 14:30:25  │
└─────────────────────────────────────────────────────────┘
```

### 状态栏信息
- **系统状态**: 显示当前操作状态
- **GPU信息**: 显示GPU型号和可用性
- **内存使用**: 显示当前内存使用情况
- **当前时间**: 实时时间显示

## 故障排除

### 常见问题

**1. 启动失败**
- 检查Python版本（需要3.7+）
- 确认所有依赖包已安装
- 检查文件完整性

**2. 数据加载失败**
- 确认数据文件格式正确
- 检查必要列是否存在
- 确认数据编码为UTF-8

**3. 训练过程中断**
- 检查GPU内存是否足够
- 确认数据质量
- 调整批次大小参数

**4. 预测结果异常**
- 确认模型文件完整
- 检查预测数据格式
- 验证数据预处理一致性

### 性能优化建议

**1. 硬件配置**
- 推荐使用GPU加速训练
- 至少8GB内存
- SSD硬盘提升I/O性能

**2. 参数调优**
- 根据数据量调整序列长度
- 合理设置批次大小
- 启用贝叶斯优化获得最佳参数

**3. 数据质量**
- 确保数据连续性
- 处理异常值和缺失值
- 适当的特征工程

## 技术架构

### 系统组件
```
GUI前端 (tkinter)
    ├── gui_main.py          # 主界面
    ├── gui_components.py    # 可重用组件
    └── gui_backend.py       # 后端服务

核心算法
    ├── train.py             # 训练主程序
    ├── model.py             # IBOTCNGRU模型
    ├── data_preprocessing.py # 数据预处理
    ├── advanced_bayesian_optimizer.py # 贝叶斯优化
    └── optimization_visualizer.py     # 可视化工具
```

### 数据流程
```
原始数据 → 数据预处理 → 特征工程 → 序列构建 → 模型训练 → 结果评估 → 模型保存
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的GUI界面
- 支持IBOTCNGRU模型训练
- 贝叶斯超参数优化
- 多种可视化图表
- 模型保存和加载功能

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本系统基于深度学习技术，预测结果仅供参考，实际应用中请结合专业判断。
