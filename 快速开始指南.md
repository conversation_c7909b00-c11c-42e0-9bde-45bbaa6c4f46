# 电动汽车充电负荷预测系统 - 快速开始指南

## 🚀 快速启动

### 1. 生成演示数据
```bash
python generate_demo_data.py
```
这将生成三个演示数据文件：
- `demo_charging_data.csv` - 完整年度数据（用于训练）
- `small_demo_data.csv` - 小规模测试数据（快速测试）
- `prediction_demo_data.csv` - 预测演示数据

### 2. 启动GUI系统
```bash
python run_gui.py
```

## 📋 使用步骤

### 第一步：数据导入
1. 点击 **"数据导入"** 选项卡
2. 点击 **"浏览"** 按钮，选择 `small_demo_data.csv`
3. 点击 **"验证数据"** 检查数据格式
4. 点击 **"加载数据"** 完成导入

### 第二步：参数设置
1. 点击 **"参数设置"** 选项卡
2. 推荐的快速测试参数：
   - 序列长度：24小时
   - 计算设备：auto（自动选择）
   - 隐藏层大小：64（减少计算量）
   - 网络层数：2（减少计算量）
   - **关闭贝叶斯优化**（快速测试）
   - 交叉验证折数：3（减少计算时间）

### 第三步：开始训练
1. 点击 **"模型训练"** 选项卡
2. 点击 **"开始训练"** 按钮
3. 观察训练进度和日志输出
4. 等待训练完成（小数据集约5-10分钟）

### 第四步：查看结果
1. 训练完成后自动跳转到 **"结果查看"** 选项卡
2. 查看评估指标（MSE、RMSE、MAE等）
3. 切换不同图表类型：
   - 训练历史
   - 预测对比
   - 误差分布

### 第五步：使用预测服务
1. 点击 **"预测服务"** 选项卡
2. 加载刚才训练的模型
3. 选择 `prediction_demo_data.csv` 作为预测数据
4. 点击 **"开始预测"** 查看结果

## ⚡ 快速测试配置

为了快速体验系统功能，建议使用以下配置：

```
数据文件: small_demo_data.csv
序列长度: 24
隐藏层大小: 64
网络层数: 2
学习率: 0.001
批次大小: 32
关闭贝叶斯优化
交叉验证折数: 3
```

这个配置可以在几分钟内完成训练，适合快速测试。

## 🔧 完整训练配置

如果要获得最佳性能，使用以下配置：

```
数据文件: demo_charging_data.csv
序列长度: 24
隐藏层大小: 128
网络层数: 3
学习率: 0.001
批次大小: 64
启用贝叶斯优化
优化调用次数: 100
交叉验证折数: 5
```

注意：完整训练可能需要30分钟到几小时，取决于硬件配置。

## 📊 数据格式要求

### 必需列
- **时间列**: `充电时间` 或 `Timestamp`
- **负荷列**: `总有功功率_总和(kW)` 或 `Charging_Load_kW`

### 可选列（天气数据）
- `降水量(mm)` 或 `Precipitation_mm`
- `平均气温(℃)` 或 `Average_Temperature_C`
- `最低气温(℃)` 或 `Min_Temperature_C`
- `最高气温(℃)` 或 `Max_Temperature_C`

### 数据示例
```csv
充电时间,总有功功率_总和(kW),降水量(mm),平均气温(℃),最低气温(℃),最高气温(℃)
2023-01-01 00:00:00,45.23,0.0,15.2,12.1,18.5
2023-01-01 01:00:00,38.67,0.0,14.8,11.9,18.1
...
```

## 🎯 功能特点

### 核心算法
- **IBOTCNGRU模型**: 集成TCN、GRU和注意力机制
- **贝叶斯优化**: 自动寻找最优超参数
- **时间序列交叉验证**: 确保模型泛化能力

### 界面特点
- **一键式操作**: 无需编程知识
- **实时进度**: 训练过程可视化
- **多种图表**: 全面的结果展示
- **配置管理**: 保存和加载参数配置

### 导出功能
- **HTML报告**: 完整的训练结果报告
- **预测数据**: CSV/Excel格式导出
- **模型文件**: 保存训练好的模型

## 🔍 故障排除

### 常见问题

**Q: 启动时提示缺少依赖包**
A: 运行 `pip install torch pandas numpy matplotlib scikit-learn psutil`

**Q: 数据加载失败**
A: 检查数据文件编码是否为UTF-8，确认必需列存在

**Q: 训练过程中内存不足**
A: 减少批次大小或序列长度，或使用更小的数据集

**Q: GPU不可用**
A: 将设备设置改为"cpu"，或安装CUDA驱动

### 性能建议

**硬件要求**
- 最低：4GB内存，CPU
- 推荐：8GB内存，支持CUDA的GPU
- 最佳：16GB内存，RTX系列GPU

**参数调优**
- 小数据集（<1000行）：序列长度12-24，隐藏层64
- 中等数据集（1000-10000行）：序列长度24-48，隐藏层128
- 大数据集（>10000行）：序列长度48-168，隐藏层256

## 📞 技术支持

如遇到问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 数据文件格式是否符合要求
4. 系统内存是否充足

---

**祝您使用愉快！** 🎉
